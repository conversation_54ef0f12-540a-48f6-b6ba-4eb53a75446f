# Context Path for API
server.port = 8444
server.ip = ***********
server.protocol = http
server.url=${server.protocol}://${server.ip}:${server.port}/api

server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Database configuration
spring.datasource.url=jdbc:postgresql://*************:5432/caio_martins_db
spring.datasource.jdbcUrl=${spring.datasource.url}
spring.datasource.username=caio_martins_user
spring.datasource.password=yry29pasta4uv75awsr9
spring.datasource.driverClassName=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Configurações do Flyway para usar o datasource do plugdrive (PostgreSQL)
spring.flyway.enabled=true
spring.flyway.url=${spring.datasource.url}
spring.flyway.jdbcUrl=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.locations=classpath:db/migration
spring.flyway.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.flyway.driverClassName=org.postgresql.Driver
spring.flyway.outOfOrder=true

spring.devtools.restart.enabled=false

# Swagger
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true

# Mostrar os detalhes da instrução SQL
#logging.level.org.hibernate.SQL=DEBUG

# Mostrar os valores dos parâmetros vinculados nas instruções SQL
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

jwt.access-secret-key=8hqha//nNs/foEOyG8baRI2Cv6T32mvcBTRJ+LWcf7sMtoZtVuZVX40hz04EtAJOy+V0eho1fmqE4pEe4kiqmwcj8EkbpQZg7F9eOjiJd5YFgHqDDB/HP320D0XD+8GNIJ662HBs+EhACWJGpoI+3E4rZ9vNW0F6KZDpET7pjiDkmOipHlQ1MXcLwbpdBSamQe4gvhlZmz4kiaGHpMmiOsXEcHEBOF/GNqdkGIu5/SBTiqXfLIn2F5KwO/LlMJts2qul5pllsC3wHB+Xkcd4o3MXKa1CMY7qzolah6gVD6lrZtqswpbCDm60m96c7GA8roIKT8c4zfvJPGyesdhygw==
jwt.refresh-secret-key=IpCLNPUD20mgh+xk+UcKMTmzyymR6WY6YEcphxAQfbEta1sbnhBGsGs+qYLus+cH/qUzNsyXyprJ71goqtdIumL34LiMUC5E4VDuji5A1udbzODOzhwW7yti64e9d5dpbtmn/OKZrjzmmyq3eyapl4rc0qu+nZsu6ccCB0gpZiTQaQRQ2p5CK//x7zTOTX3NhDdHd95hlLNyttAc7f0PxxDAvopAw5QTl8mdwlq41eu1KRJ8BhjLi173rn2qeFZqZENR11c4zE1+HsTmGMOVE/Sdr2n0wmMbwCEdjYcVH/G3fRBdde6WPsfw4Q08kT44u29bD3IQSvi+8a9URGp34A==
crypto.secret-key=qN6GZ82+/qc9QkSG/g4TORe82VLjij4Rh9av5o6e66M=

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=oqzr oroa rznv ollb
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.enable=false
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.properties.mail.debug=false
spring.mail.default-encoding=UTF-8

# Desabilitar verificação de saúde do email
management.health.mail.enabled=false

google.client.id=750626573174-vhcrd4c7r7p59h0mfa30ftm8p552tge7.apps.googleusercontent.com

# AWS S3 Configuration
aws.s3.bucket-name=caio-martins-app-prod
aws.s3.region=us-east-1
aws.s3.access-key=********************
aws.s3.secret-key=K4ODLyfLxPtFOmUgd+lcJSJsbAf3sFNfKn6hxZYF
aws.s3.base-url=https://caio-martins-app-prod.s3.amazonaws.com

CHECK_PERMISSION=false

# Jackson - Configuração para serialização de datas
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=America/Sao_Paulo
spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss

# Configurações de upload multipart
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true