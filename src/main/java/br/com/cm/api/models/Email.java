package br.com.cm.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "emails", schema = "public")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@EntityListeners(AuditingEntityListener.class)
public class Email {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "email_address", nullable = false)
    private String emailAddress;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_type_id", nullable = false)
    private EmailType emailType;
    
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Métodos para aceitar emailTypeId no JSON
    @JsonProperty("emailTypeId")
    public void setEmailTypeId(Long emailTypeId) {
        if (emailTypeId != null) {
            this.emailType = new EmailType();
            this.emailType.setId(emailTypeId);
        }
    }

    // Método para retornar emailTypeId no JSON
    @JsonProperty("emailTypeId")
    public Long getEmailTypeId() {
        return this.emailType != null ? this.emailType.getId() : null;
    }
}
