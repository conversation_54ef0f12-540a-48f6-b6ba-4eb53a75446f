package br.com.cm.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "phones", schema = "public")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Phone {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "number", nullable = false)
    private String number;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phone_type_id", nullable = false)
    private PhoneType phoneType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "area_code_id", nullable = false)
    private AreaCode areaCode;

    // Métodos para aceitar phoneTypeId no JSON
    @JsonProperty("phoneTypeId")
    public void setPhoneTypeId(Long phoneTypeId) {
        if (phoneTypeId != null) {
            this.phoneType = new PhoneType();
            this.phoneType.setId(phoneTypeId);
        }
    }

    // Método para retornar phoneTypeId no JSON
    @JsonProperty("phoneTypeId")
    public Long getPhoneTypeId() {
        return this.phoneType != null ? this.phoneType.getId() : null;
    }

    // Métodos para aceitar areaCodeId no JSON
    @JsonProperty("areaCodeId")
    public void setAreaCodeId(Long areaCodeId) {
        if (areaCodeId != null) {
            this.areaCode = new AreaCode();
            this.areaCode.setId(areaCodeId);
        }
    }

    // Método para retornar areaCodeId no JSON
    @JsonProperty("areaCodeId")
    public Long getAreaCodeId() {
        return this.areaCode != null ? this.areaCode.getId() : null;
    }
}
