package br.com.cm.api.models;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "institution_positions", schema = "geral")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@EntityListeners(AuditingEntityListener.class)
public class InstitutionPosition {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "institution_config_id", nullable = false)
    @JsonBackReference
    private InstitutionConfig institutionConfig;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "position_type_id", nullable = false)
    private PositionType positionType;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "cpf", nullable = false, length = 11)
    private String cpf;
    
    @Column(name = "birth_date")
    private LocalDate birthDate;
    
    @Column(name = "marital_status")
    private String maritalStatus;
    
    @Column(name = "profession")
    private String profession;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "address_id")
    private Address address;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "phone_id")
    private Phone phone;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_id")
    private Email email;
    
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Métodos para aceitar positionTypeId no JSON
    @JsonProperty("positionTypeId")
    public void setPositionTypeId(Long positionTypeId) {
        if (positionTypeId != null) {
            this.positionType = new PositionType();
            this.positionType.setId(positionTypeId);
        }
    }

    // Método para retornar positionTypeId no JSON
    @JsonProperty("positionTypeId")
    public Long getPositionTypeId() {
        return this.positionType != null ? this.positionType.getId() : null;
    }

    // Métodos para aceitar institutionConfigId no JSON
    @JsonProperty("institutionConfigId")
    public void setInstitutionConfigId(Long institutionConfigId) {
        if (institutionConfigId != null) {
            this.institutionConfig = new InstitutionConfig();
            this.institutionConfig.setId(institutionConfigId);
        }
    }

    // Método para retornar institutionConfigId no JSON
    @JsonProperty("institutionConfigId")
    public Long getInstitutionConfigId() {
        return this.institutionConfig != null ? this.institutionConfig.getId() : null;
    }
}
