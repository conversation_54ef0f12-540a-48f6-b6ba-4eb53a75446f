package br.com.cm.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "addresses", schema = "public")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@EntityListeners(AuditingEntityListener.class)
public class Address {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "street", nullable = false)
    private String street;

    @Column(name = "number")
    private String number;

    @Column(name = "complement")
    private String complement;

    @Column(name = "neighborhood")
    private String neighborhood;

    @Column(name = "postal_code")
    private String postalCode;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "address_type_id")
    private AddressType addressType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "city_id")
    private City city;
    
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Métodos para aceitar addressTypeId no JSON
    @JsonProperty("addressTypeId")
    public void setAddressTypeId(Long addressTypeId) {
        if (addressTypeId != null) {
            this.addressType = new AddressType();
            this.addressType.setId(addressTypeId);
        }
    }

    // Método para retornar addressTypeId no JSON
    @JsonProperty("addressTypeId")
    public Long getAddressTypeId() {
        return this.addressType != null ? this.addressType.getId() : null;
    }

    // Métodos para aceitar cityId no JSON
    @JsonProperty("cityId")
    public void setCityId(Long cityId) {
        if (cityId != null) {
            this.city = new City();
            this.city.setId(cityId);
        }
    }

    // Método para retornar cityId no JSON
    @JsonProperty("cityId")
    public Long getCityId() {
        return this.city != null ? this.city.getId() : null;
    }
}
