package br.com.cm.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "institution_configs", schema = "geral")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@EntityListeners(AuditingEntityListener.class)
public class InstitutionConfig {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "uel_name", nullable = false)
    private String uelName;
    
    @Column(name = "logo_s3_key")
    private String logoS3Key;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "institution_id", nullable = false)
    private Institution institution;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "smtp_config_id")
    private SmtpConfig smtpConfig;
    
    @OneToMany(mappedBy = "institutionConfig", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<InstitutionPosition> positions;
    
    @CreatedDate
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Método para aceitar institutionId no JSON
    @JsonProperty("institutionId")
    public void setInstitutionId(Long institutionId) {
        if (institutionId != null) {
            this.institution = new Institution();
            this.institution.setId(institutionId);
        }
    }

    // Método para retornar institutionId no JSON
    @JsonProperty("institutionId")
    public Long getInstitutionId() {
        return this.institution != null ? this.institution.getId() : null;
    }
}
