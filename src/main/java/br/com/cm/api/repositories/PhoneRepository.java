package br.com.cm.api.repositories;

import br.com.cm.api.models.Phone;
import br.com.cm.api.util.generics.GenericRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PhoneRepository extends GenericRepository<Phone, Long> {
    
    @Query("SELECT p FROM Phone p LEFT JOIN FETCH p.phoneType LEFT JOIN FETCH p.areaCode WHERE p.id = :id")
    Optional<Phone> findByIdWithDetails(@Param("id") Long id);
    
    @Query("SELECT p FROM Phone p WHERE p.number = :number")
    List<Phone> findByNumber(@Param("number") String number);
    
    @Query("SELECT p FROM Phone p WHERE p.number = :number AND p.areaCode.code = :areaCode")
    Optional<Phone> findByNumberAndAreaCode(@Param("number") String number, @Param("areaCode") String areaCode);

    @Query("SELECT p FROM Phone p WHERE p.number = :number AND p.areaCode.id = :areaCodeId")
    Optional<Phone> findByNumberAndAreaCodeId(@Param("number") String number, @Param("areaCodeId") Long areaCodeId);
    
    @Query("SELECT p FROM Phone p WHERE p.areaCode.id = :areaCodeId")
    List<Phone> findByAreaCodeId(@Param("areaCodeId") Long areaCodeId);
    
    @Query("SELECT p FROM Phone p WHERE p.areaCode.code = :areaCode")
    List<Phone> findByAreaCode(@Param("areaCode") String areaCode);
    
    @Query("SELECT p FROM Phone p WHERE p.phoneType.id = :phoneTypeId")
    List<Phone> findByPhoneTypeId(@Param("phoneTypeId") Long phoneTypeId);
    
    @Query("SELECT p FROM Phone p WHERE LOWER(p.phoneType.name) = LOWER(:phoneTypeName)")
    List<Phone> findByPhoneTypeNameIgnoreCase(@Param("phoneTypeName") String phoneTypeName);
    
    @Query("SELECT p FROM Phone p LEFT JOIN FETCH p.phoneType LEFT JOIN FETCH p.areaCode ORDER BY p.areaCode.code ASC, p.number ASC")
    List<Phone> findAllWithDetailsOrderByAreaCodeAndNumber();
    
    @Query("SELECT p FROM Phone p WHERE p.number LIKE CONCAT(:number, '%')")
    List<Phone> findByNumberStartingWith(@Param("number") String number);
    
    @Query("SELECT COUNT(p) FROM Phone p WHERE p.areaCode.id = :areaCodeId")
    Long countByAreaCodeId(@Param("areaCodeId") Long areaCodeId);
    
    @Query("SELECT COUNT(p) FROM Phone p WHERE p.phoneType.id = :phoneTypeId")
    Long countByPhoneTypeId(@Param("phoneTypeId") Long phoneTypeId);
    
    @Query("SELECT p FROM Phone p WHERE LENGTH(p.number) = :length")
    List<Phone> findByNumberLength(@Param("length") Integer length);
}
