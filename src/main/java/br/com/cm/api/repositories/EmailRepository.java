package br.com.cm.api.repositories;

import br.com.cm.api.models.Email;
import br.com.cm.api.util.generics.GenericRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmailRepository extends GenericRepository<Email, Long> {
    
    @Query("SELECT e FROM Email e LEFT JOIN FETCH e.emailType WHERE e.id = :id")
    Optional<Email> findByIdWithEmailType(@Param("id") Long id);
    
    @Query("SELECT e FROM Email e WHERE LOWER(e.emailAddress) = LOWER(:emailAddress)")
    Optional<Email> findByEmailAddressIgnoreCase(@Param("emailAddress") String emailAddress);

    @Query("SELECT e FROM Email e WHERE e.emailAddress = :emailAddress")
    Optional<Email> findByEmailAddress(@Param("emailAddress") String emailAddress);
    
    @Query("SELECT e FROM Email e WHERE LOWER(e.emailAddress) LIKE LOWER(CONCAT('%', :emailAddress, '%'))")
    List<Email> findByEmailAddressContainingIgnoreCase(@Param("emailAddress") String emailAddress);
    
    @Query("SELECT e FROM Email e WHERE e.emailType.id = :emailTypeId")
    List<Email> findByEmailTypeId(@Param("emailTypeId") Long emailTypeId);
    
    @Query("SELECT e FROM Email e WHERE LOWER(e.emailType.name) = LOWER(:emailTypeName)")
    List<Email> findByEmailTypeNameIgnoreCase(@Param("emailTypeName") String emailTypeName);
    
    @Query("SELECT e FROM Email e LEFT JOIN FETCH e.emailType ORDER BY e.emailAddress ASC")
    List<Email> findAllWithEmailTypeOrderByEmailAddress();
    
    @Query("SELECT e FROM Email e WHERE e.emailAddress LIKE CONCAT('%', :domain)")
    List<Email> findByDomain(@Param("domain") String domain);
    
    @Query("SELECT COUNT(e) FROM Email e WHERE e.emailType.id = :emailTypeId")
    Long countByEmailTypeId(@Param("emailTypeId") Long emailTypeId);
}
