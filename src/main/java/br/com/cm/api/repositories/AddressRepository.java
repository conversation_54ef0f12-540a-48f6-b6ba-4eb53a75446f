package br.com.cm.api.repositories;

import br.com.cm.api.models.Address;
import br.com.cm.api.util.generics.GenericRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AddressRepository extends GenericRepository<Address, Long> {
    
    @Query("SELECT a FROM Address a LEFT JOIN FETCH a.addressType LEFT JOIN FETCH a.city c LEFT JOIN FETCH c.federationUnit fu LEFT JOIN FETCH fu.country WHERE a.id = :id")
    Optional<Address> findByIdWithDetails(@Param("id") Long id);
    
    @Query("SELECT a FROM Address a WHERE a.postalCode = :postalCode")
    List<Address> findByPostalCode(@Param("postalCode") String postalCode);
    
    @Query("SELECT a FROM Address a WHERE LOWER(a.street) LIKE LOWER(CONCAT('%', :street, '%'))")
    List<Address> findByStreetContaining(@Param("street") String street);

    @Query("SELECT a FROM Address a WHERE LOWER(a.neighborhood) LIKE LOWER(CONCAT('%', :neighborhood, '%'))")
    List<Address> findByNeighborhoodContaining(@Param("neighborhood") String neighborhood);
    
    @Query("SELECT a FROM Address a WHERE a.city.id = :cityId")
    List<Address> findByCityId(@Param("cityId") Long cityId);
    
    @Query("SELECT a FROM Address a WHERE a.addressType.id = :addressTypeId")
    List<Address> findByAddressTypeId(@Param("addressTypeId") Long addressTypeId);
    
    @Query("SELECT a FROM Address a LEFT JOIN FETCH a.addressType LEFT JOIN FETCH a.city c LEFT JOIN FETCH c.federationUnit fu LEFT JOIN FETCH fu.country ORDER BY a.street ASC")
    List<Address> findAllWithDetailsOrderByStreet();
    
    @Query("SELECT a FROM Address a WHERE a.city.federationUnit.id = :federationUnitId")
    List<Address> findByFederationUnitId(@Param("federationUnitId") Long federationUnitId);
    
    @Query("SELECT a FROM Address a WHERE a.city.federationUnit.country.id = :countryId")
    List<Address> findByCountryId(@Param("countryId") Long countryId);
    
    @Query("SELECT a FROM Address a WHERE a.street = :street AND a.number = :number AND a.city.id = :cityId")
    Optional<Address> findByStreetAndNumberAndCity(@Param("street") String street, @Param("number") String number, @Param("cityId") Long cityId);
}
