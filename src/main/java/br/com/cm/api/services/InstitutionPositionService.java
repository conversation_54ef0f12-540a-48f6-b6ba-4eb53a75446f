package br.com.cm.api.services;

import br.com.cm.api.models.*;
import br.com.cm.api.repositories.*;
import br.com.cm.api.util.generics.GenericService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class InstitutionPositionService extends GenericService<InstitutionPosition, Long> {

    @Autowired
    private InstitutionPositionRepository institutionPositionRepository;

    @Autowired
    private AddressRepository addressRepository;

    @Autowired
    private PhoneRepository phoneRepository;

    @Autowired
    private EmailRepository emailRepository;

    @Autowired
    private InstitutionConfigRepository institutionConfigRepository;

    @Autowired
    private PositionTypeRepository positionTypeRepository;

    @Autowired
    private AddressTypeRepository addressTypeRepository;

    @Autowired
    private CityRepository cityRepository;

    @Autowired
    private PhoneTypeRepository phoneTypeRepository;

    @Autowired
    private AreaCodeRepository areaCodeRepository;

    @Autowired
    private EmailTypeRepository emailTypeRepository;

    @PostConstruct
    private void init() {
        super.setGenericRepository(institutionPositionRepository);
    }

    public Optional<InstitutionPosition> findByIdWithDetails(Long id) {
        return institutionPositionRepository.findByIdWithDetails(id);
    }

    public List<InstitutionPosition> findByInstitutionConfigId(Long institutionConfigId) {
        return institutionPositionRepository.findByInstitutionConfigId(institutionConfigId);
    }

    public List<InstitutionPosition> findByPositionTypeId(Long positionTypeId) {
        return institutionPositionRepository.findByPositionTypeId(positionTypeId);
    }

    public Optional<InstitutionPosition> findByInstitutionConfigIdAndPositionTypeId(Long institutionConfigId, Long positionTypeId) {
        return institutionPositionRepository.findByInstitutionConfigIdAndPositionTypeId(institutionConfigId, positionTypeId);
    }

    public List<InstitutionPosition> findByCpf(String cpf) {
        return institutionPositionRepository.findByCpf(cpf);
    }

    public List<InstitutionPosition> findByNameContainingIgnoreCase(String name) {
        return institutionPositionRepository.findByNameContainingIgnoreCase(name);
    }

    public List<InstitutionPosition> findByBirthDate(LocalDate birthDate) {
        return institutionPositionRepository.findByBirthDate(birthDate);
    }

    public List<InstitutionPosition> findByBirthDateBetween(LocalDate startDate, LocalDate endDate) {
        return institutionPositionRepository.findByBirthDateBetween(startDate, endDate);
    }

    public List<InstitutionPosition> findByMaritalStatusIgnoreCase(String maritalStatus) {
        return institutionPositionRepository.findByMaritalStatusIgnoreCase(maritalStatus);
    }

    public List<InstitutionPosition> findByProfessionContainingIgnoreCase(String profession) {
        return institutionPositionRepository.findByProfessionContainingIgnoreCase(profession);
    }

    public List<InstitutionPosition> findAllWithDetailsOrderByInstitutionAndPosition() {
        return institutionPositionRepository.findAllWithDetailsOrderByInstitutionAndPosition();
    }

    public List<InstitutionPosition> findAllWithAddress() {
        return institutionPositionRepository.findAllWithAddress();
    }

    public List<InstitutionPosition> findAllWithPhone() {
        return institutionPositionRepository.findAllWithPhone();
    }

    public List<InstitutionPosition> findAllWithEmail() {
        return institutionPositionRepository.findAllWithEmail();
    }

    public Long countByInstitutionConfigId(Long institutionConfigId) {
        return institutionPositionRepository.countByInstitutionConfigId(institutionConfigId);
    }

    public Long countByPositionTypeId(Long positionTypeId) {
        return institutionPositionRepository.countByPositionTypeId(positionTypeId);
    }

    @Override
    public InstitutionPosition findById(Long id) {
        return institutionPositionRepository.findById(id).orElse(null);
    }

    @Override
    @Transactional
    public InstitutionPosition save(InstitutionPosition institutionPosition) {
        // Salvar ou buscar endereço
        if (institutionPosition.getAddress() != null) {
            Address address = saveOrFindAddress(institutionPosition.getAddress());
            institutionPosition.setAddress(address);
        }

        // Salvar ou buscar telefone
        if (institutionPosition.getPhone() != null) {
            Phone phone = saveOrFindPhone(institutionPosition.getPhone());
            institutionPosition.setPhone(phone);
        }

        // Salvar ou buscar email
        if (institutionPosition.getEmail() != null) {
            Email email = saveOrFindEmail(institutionPosition.getEmail());
            institutionPosition.setEmail(email);
        }

        // Configurar relacionamentos obrigatórios se necessário
        if (institutionPosition.getInstitutionConfig() == null && institutionPosition.getInstitutionConfigId() != null) {
            InstitutionConfig institutionConfig = institutionConfigRepository.findById(institutionPosition.getInstitutionConfigId())
                .orElseThrow(() -> new RuntimeException("InstitutionConfig não encontrada"));
            institutionPosition.setInstitutionConfig(institutionConfig);
        }

        if (institutionPosition.getPositionType() == null && institutionPosition.getPositionTypeId() != null) {
            PositionType positionType = positionTypeRepository.findById(institutionPosition.getPositionTypeId())
                .orElseThrow(() -> new RuntimeException("PositionType não encontrado"));
            institutionPosition.setPositionType(positionType);
        }

        return institutionPositionRepository.save(institutionPosition);
    }

    @Override
    public void delete(Long id) {
        institutionPositionRepository.deleteById(id);
    }

    private Address saveOrFindAddress(Address address) {
        // Configurar relacionamentos do endereço
        if (address.getAddressType() == null && address.getAddressTypeId() != null) {
            AddressType addressType = addressTypeRepository.findById(address.getAddressTypeId())
                .orElseThrow(() -> new RuntimeException("AddressType não encontrado"));
            address.setAddressType(addressType);
        }

        if (address.getCity() == null && address.getCityId() != null) {
            City city = cityRepository.findById(address.getCityId())
                .orElseThrow(() -> new RuntimeException("City não encontrada"));
            address.setCity(city);
        }

        // Verificar se já existe um endereço igual
        if (address.getStreet() != null && address.getNumber() != null && address.getCityId() != null) {
            return addressRepository.findByStreetAndNumberAndCity(
                address.getStreet(),
                address.getNumber(),
                address.getCityId()
            ).orElseGet(() -> addressRepository.save(address));
        }

        return addressRepository.save(address);
    }

    private Phone saveOrFindPhone(Phone phone) {
        // Configurar relacionamentos do telefone
        if (phone.getPhoneType() == null && phone.getPhoneTypeId() != null) {
            PhoneType phoneType = phoneTypeRepository.findById(phone.getPhoneTypeId())
                .orElseThrow(() -> new RuntimeException("PhoneType não encontrado"));
            phone.setPhoneType(phoneType);
        }

        if (phone.getAreaCode() == null && phone.getAreaCodeId() != null) {
            AreaCode areaCode = areaCodeRepository.findById(phone.getAreaCodeId())
                .orElseThrow(() -> new RuntimeException("AreaCode não encontrado"));
            phone.setAreaCode(areaCode);
        }

        return phoneRepository.save(phone);
    }

    private Email saveOrFindEmail(Email email) {
        // Configurar relacionamentos do email
        if (email.getEmailType() == null && email.getEmailTypeId() != null) {
            EmailType emailType = emailTypeRepository.findById(email.getEmailTypeId())
                .orElseThrow(() -> new RuntimeException("EmailType não encontrado"));
            email.setEmailType(emailType);
        }

        return emailRepository.save(email);
    }
}
