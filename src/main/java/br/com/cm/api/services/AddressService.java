package br.com.cm.api.services;

import br.com.cm.api.models.Address;
import br.com.cm.api.repositories.AddressRepository;
import br.com.cm.api.util.generics.GenericService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AddressService extends GenericService<Address, Long> {

    @Autowired
    private AddressRepository addressRepository;

    @PostConstruct
    private void init() {
        super.setGenericRepository(addressRepository);
    }

    public Optional<Address> findByIdWithDetails(Long id) {
        return addressRepository.findByIdWithDetails(id);
    }

    public List<Address> findByPostalCode(String postalCode) {
        return addressRepository.findByPostalCode(postalCode);
    }

    public List<Address> findByStreetContaining(String street) {
        return addressRepository.findByStreetContaining(street);
    }

    public List<Address> findByNeighborhoodContaining(String neighborhood) {
        return addressRepository.findByNeighborhoodContaining(neighborhood);
    }

    public List<Address> findByCityId(Long cityId) {
        return addressRepository.findByCityId(cityId);
    }

    public List<Address> findByAddressTypeId(Long addressTypeId) {
        return addressRepository.findByAddressTypeId(addressTypeId);
    }

    public List<Address> findAllWithDetailsOrderByStreet() {
        return addressRepository.findAllWithDetailsOrderByStreet();
    }

    public List<Address> findByFederationUnitId(Long federationUnitId) {
        return addressRepository.findByFederationUnitId(federationUnitId);
    }

    public List<Address> findByCountryId(Long countryId) {
        return addressRepository.findByCountryId(countryId);
    }

    public Optional<Address> findByStreetAndNumberAndCity(String street, String number, Long cityId) {
        return addressRepository.findByStreetAndNumberAndCity(street, number, cityId);
    }

    @Override
    public Address findById(Long id) {
        return addressRepository.findById(id).orElse(null);
    }

    @Override
    public Address save(Address entity) {
        return addressRepository.save(entity);
    }

    @Override
    public void delete(Long id) {
        addressRepository.deleteById(id);
    }
}
