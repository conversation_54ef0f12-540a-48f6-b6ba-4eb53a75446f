import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, AbstractControl, ValidationErrors } from '@angular/forms';
import { InstitutionConfigService } from '../../../../services/institution-config.service';
import {
  InstitutionPosition,
  PositionType
} from '../../../../models/institution-config.model';
import { FederationUnitService } from '../../../../services/federation-unit.service';
import { CityService } from '../../../../services/city.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-institution-position-form',
  templateUrl: './institution-position-form.component.html',
  styleUrls: ['./institution-position-form.component.scss']
})
export class InstitutionPositionFormComponent implements OnInit, OnChanges {

  @Input() institutionConfigId?: number;
  @Input() positions: InstitutionPosition[] = [];
  @Input() positionTypes: PositionType[] = [];

  // Validador customizado para campos de select
  private selectRequired(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (value === null || value === undefined || value === '') {
      return { required: true };
    }
    return null;
  }

  form: FormGroup;
  loading = false;
  saving = false;

  // Dados para dropdowns
  federationUnits: any[] = [];
  cities: any[] = [];
  addressTypes: any[] = [];
  phoneTypes: any[] = [];
  emailTypes: any[] = [];
  areaCodes: any[] = [];



  // Getters para converter dados para o formato esperado pelo vlo-select
  get federationUnitOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const federationOptions = this.federationUnits.map(unit => ({
      value: unit.id?.toString() || '',
      label: `${unit.name} (${unit.abbreviation})`
    }));

    return [...options, ...federationOptions];
  }

  get cityOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const cityOptionsData = this.cities.map(city => ({
      value: city.id?.toString() || '',
      label: city.name || ''
    }));

    return [...options, ...cityOptionsData];
  }

  get addressTypeOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const addressTypeOptionsData = this.addressTypes.map(type => ({
      value: type.id?.toString() || '',
      label: type.name || ''
    }));

    return [...options, ...addressTypeOptionsData];
  }

  get phoneTypeOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const phoneTypeOptionsData = this.phoneTypes.map(type => ({
      value: type.id?.toString() || '',
      label: type.name || ''
    }));

    return [...options, ...phoneTypeOptionsData];
  }

  get emailTypeOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const emailTypeOptionsData = this.emailTypes.map(type => ({
      value: type.id?.toString() || '',
      label: type.name || ''
    }));

    return [...options, ...emailTypeOptionsData];
  }

  get areaCodeOptions() {
    const options = [
      { value: '', label: '-- Selecione --' }
    ];

    const areaCodeOptionsData = this.areaCodes.map(code => ({
      value: code.id?.toString() || '',
      label: code.code || ''
    }));

    return [...options, ...areaCodeOptionsData];
  }

  // Estados civis predefinidos
  maritalStatuses = [
    { value: 'Solteiro(a)', label: 'Solteiro(a)' },
    { value: 'Casado(a)', label: 'Casado(a)' },
    { value: 'Divorciado(a)', label: 'Divorciado(a)' },
    { value: 'Viúvo(a)', label: 'Viúvo(a)' },
    { value: 'União Estável', label: 'União Estável' }
  ];

  // Getter para converter positionTypes para o formato esperado pelo vlo-select
  get positionTypeOptions() {
    const options = [
      { value: '', label: '-- Selecione --' },
      ...this.positionTypes.map(pt => ({
        value: pt.id?.toString() || '',
        label: pt.name
      }))
    ];
    return options;
  }

  constructor(
    private fb: FormBuilder,
    private institutionConfigService: InstitutionConfigService,
    private federationUnitService: FederationUnitService,
    private cityService: CityService
  ) {
    this.form = this.createForm();
  }

  ngOnInit(): void {
    this.loadDropdownData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['positions'] && this.positions) {
      this.populatePositions();
    }
    if (changes['positionTypes'] && this.positionTypes) {
      // Position types atualizados
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      positions: this.fb.array([])
    });
  }

  private loadDropdownData(): void {
    // Carregar estados (federation units) sem paginação
    this.federationUnitService.getAllWithoutPagination().subscribe({
      next: (data: any) => {
        this.federationUnits = Array.isArray(data) ? data : [];
      },
      error: (error: any) => console.error('Erro ao carregar estados:', error)
    });

    // Não carregar todas as cidades inicialmente - será carregado quando selecionar o estado
    this.cities = [];

    // Carregar tipos de endereço
    this.institutionConfigService.getAddressTypes().subscribe({
      next: (data: any) => {
        this.addressTypes = Array.isArray(data) ? data : [];
      },
      error: (error: any) => console.error('Erro ao carregar tipos de endereço:', error)
    });

    // Carregar tipos de telefone
    this.institutionConfigService.getPhoneTypes().subscribe({
      next: (data: any) => {
        this.phoneTypes = Array.isArray(data) ? data : [];
      },
      error: (error: any) => console.error('Erro ao carregar tipos de telefone:', error)
    });

    // Carregar tipos de email
    this.institutionConfigService.getEmailTypes().subscribe({
      next: (data: any) => {
        this.emailTypes = Array.isArray(data) ? data : [];
      },
      error: (error: any) => console.error('Erro ao carregar tipos de email:', error)
    });

    // Carregar códigos de área
    this.institutionConfigService.getAreaCodes().subscribe({
      next: (data: any) => {
        this.areaCodes = Array.isArray(data) ? data : [];
      },
      error: (error: any) => console.error('Erro ao carregar códigos de área:', error)
    });
  }

  private populatePositions(): void {
    const positionsArray = this.positionsFormArray;
    positionsArray.clear();

    this.positions.forEach((position, index) => {
      const formGroup = this.createPositionFormGroup(position);
      positionsArray.push(formGroup);

      // Se a posição tem uma cidade, carregar o estado correspondente
      if (position.address?.cityId) {
        this.loadFederationUnitForCity(position.address.cityId, index);
      }
    });
  }

  private createPositionFormGroup(position?: InstitutionPosition): FormGroup {
    return this.fb.group({
      id: [position?.id || null],
      positionTypeId: [position?.positionTypeId?.toString() || '', this.selectRequired],
      name: [position?.name || '', [Validators.required, Validators.maxLength(255)]],
      cpf: [position?.cpf || '', [Validators.required]],
      birthDate: [position?.birthDate || null],
      maritalStatus: [position?.maritalStatus || ''],
      profession: [position?.profession || ''],

      // Endereço
      address: this.fb.group({
        street: [position?.address?.street || '', Validators.required],
        number: [position?.address?.number || ''],
        complement: [position?.address?.complement || ''],
        neighborhood: [position?.address?.neighborhood || ''],
        postalCode: [position?.address?.postalCode || ''],
        federationUnitId: [null], // Campo auxiliar para filtrar cidades, não é salvo
        cityId: [position?.address?.cityId?.toString() || '', this.selectRequired],
        addressTypeId: [position?.address?.addressTypeId?.toString() || '', this.selectRequired]
      }),

      // Telefone
      phone: this.fb.group({
        number: [position?.phone?.number || '', Validators.required],
        phoneTypeId: [position?.phone?.phoneTypeId?.toString() || '', this.selectRequired],
        areaCodeId: [position?.phone?.areaCodeId?.toString() || '', this.selectRequired]
      }),

      // Email
      email: this.fb.group({
        emailAddress: [position?.email?.emailAddress || '', [Validators.required, Validators.email]],
        emailTypeId: [position?.email?.emailTypeId?.toString() || '', this.selectRequired]
      })
    });
  }

  get positionsFormArray(): FormArray {
    return this.form.get('positions') as FormArray;
  }

  getPositionFormGroup(index: number): FormGroup {
    return this.positionsFormArray.at(index) as FormGroup;
  }

  onAddPosition(): void {
    this.positionsFormArray.push(this.createPositionFormGroup());
  }

  onRemovePosition(index: number): void {
    const position = this.positionsFormArray.at(index).value;

    if (position.id) {
      if (confirm('Tem certeza que deseja excluir esta posição?')) {
        this.institutionConfigService.deleteInstitutionPosition(position.id).subscribe({
          next: () => {
            this.positionsFormArray.removeAt(index);
          },
          error: (error: any) => {
            console.error('Erro ao excluir posição:', error);
            alert('Erro ao excluir posição. Tente novamente.');
          }
        });
      }
    } else {
      this.positionsFormArray.removeAt(index);
    }
  }

  onSavePosition(index: number): void {
    const positionForm = this.getPositionFormGroup(index);

    // Forçar atualização dos valores dos componentes vlo
    this.updateFormValuesFromComponents(positionForm);

    if (positionForm.invalid) {
      positionForm.markAllAsTouched();

      // Log detalhado dos campos inválidos
      this.logInvalidFields(positionForm);

      Swal.fire({
        title: 'Campos obrigatórios',
        text: 'Por favor, preencha todos os campos obrigatórios.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return;
    }

    if (!this.institutionConfigId) {
      Swal.fire({
        title: 'Erro',
        text: 'ID da configuração da instituição não encontrado.',
        icon: 'error',
        confirmButtonText: 'OK'
      });
      return;
    }

    this.saving = true;
    const formData = this.preparePositionData(positionForm.value);

    const request = formData.id
      ? this.institutionConfigService.updateInstitutionPosition(formData.id, formData)
      : this.institutionConfigService.createInstitutionPosition(formData);

    request.subscribe({
      next: (response: any) => {
        this.saving = false;
        positionForm.patchValue({ id: response.id });
        Swal.fire({
          title: 'Sucesso!',
          text: 'Cargo salvo com sucesso!',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      },
      error: (error: any) => {
        console.error('Erro ao salvar cargo:', error);
        this.saving = false;
        Swal.fire({
          title: 'Erro',
          text: 'Erro ao salvar cargo. Tente novamente.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  private preparePositionData(formValue: any): any {
    // Preparar dados do endereço
    const addressData = formValue.address ? {
      street: formValue.address.street,
      number: formValue.address.number,
      complement: formValue.address.complement,
      neighborhood: formValue.address.neighborhood,
      postalCode: formValue.address.postalCode,
      cityId: parseInt(formValue.address.cityId),
      addressTypeId: parseInt(formValue.address.addressTypeId)
    } : null;

    // Preparar dados do telefone
    const phoneData = formValue.phone ? {
      number: formValue.phone.number,
      phoneTypeId: parseInt(formValue.phone.phoneTypeId),
      areaCodeId: parseInt(formValue.phone.areaCodeId)
    } : null;

    // Preparar dados do email
    const emailData = formValue.email ? {
      emailAddress: formValue.email.emailAddress,
      emailTypeId: parseInt(formValue.email.emailTypeId)
    } : null;

    return {
      id: formValue.id,
      institutionConfigId: this.institutionConfigId,
      positionTypeId: parseInt(formValue.positionTypeId),
      name: formValue.name,
      cpf: formValue.cpf.replace(/\D/g, ''), // Remove formatação do CPF
      birthDate: formValue.birthDate,
      maritalStatus: formValue.maritalStatus,
      profession: formValue.profession,
      address: addressData,
      phone: phoneData,
      email: emailData
    };
  }

  getPositionTypeName(positionTypeId: number): string {
    const positionType = this.positionTypes.find(pt => pt.id === positionTypeId);
    return positionType?.name || 'Tipo não encontrado';
  }

  // Validação de campos
  isPositionFieldInvalid(index: number, fieldPath: string): boolean {
    const positionForm = this.getPositionFormGroup(index);
    const field = positionForm.get(fieldPath);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getPositionFieldError(index: number, fieldPath: string): string {
    const positionForm = this.getPositionFormGroup(index);
    const field = positionForm.get(fieldPath);

    if (field?.errors) {
      if (field.errors['required']) return 'Este campo é obrigatório';
      if (field.errors['email']) return 'Email inválido';
      if (field.errors['pattern']) return 'Formato inválido';
      if (field.errors['maxlength']) return `Máximo de ${field.errors['maxlength'].requiredLength} caracteres`;
    }
    return '';
  }

  // Formatação de CPF
  formatCpf(cpf: string): string {
    if (!cpf) return '';
    const cleaned = cpf.replace(/\D/g, '');
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }



  onFederationUnitChange(federationUnitId: string, index: number): void {
    const positionForm = this.getPositionFormGroup(index);
    const addressForm = positionForm.get('address') as FormGroup;

    // Limpar cidade selecionada
    addressForm.get('cityId')?.setValue(null);

    if (federationUnitId && federationUnitId !== '') {
      // Carregar cidades do estado selecionado usando /list-all sem paginação
      this.cityService.getByFederationUnitId(Number(federationUnitId)).subscribe({
        next: (data: any) => {
          // Como estamos usando /list-all, os dados vêm diretamente como array
          this.cities = Array.isArray(data) ? data : [];
        },
        error: (error: any) => console.error('Erro ao carregar cidades:', error)
      });
    } else {
      // Se nenhum estado foi selecionado ou selecionou "-- Selecione --", limpar cidades
      this.cities = [];
    }
  }

  private loadFederationUnitForCity(cityId: number, index: number): void {
    // Buscar informações da cidade para obter o estado
    this.cityService.getById(cityId).subscribe({
      next: (city: any) => {
        if (city && city.federationUnit) {
          const positionForm = this.getPositionFormGroup(index);
          const addressForm = positionForm.get('address') as FormGroup;

          // Definir o estado no formulário
          addressForm.get('federationUnitId')?.setValue(city.federationUnit.id.toString());

          // Carregar as cidades deste estado
          this.onFederationUnitChange(city.federationUnit.id.toString(), index);
        }
      },
      error: (error: any) => console.error('Erro ao carregar informações da cidade:', error)
    });
  }

  private logInvalidFields(formGroup: FormGroup, prefix: string = ''): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      const fieldName = prefix ? `${prefix}.${key}` : key;

      if (control instanceof FormGroup) {
        this.logInvalidFields(control, fieldName);
      } else if (control && control.invalid) {
        // Campo inválido encontrado
      }
    });
  }



  getFormErrors(index: number): string[] {
    const errors: string[] = [];
    const positionForm = this.getPositionFormGroup(index);

    this.collectFormErrors(positionForm, '', errors);

    return errors;
  }

  private collectFormErrors(formGroup: FormGroup, prefix: string = '', errors: string[] = []): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      const fieldName = prefix ? `${prefix}.${key}` : key;

      if (control instanceof FormGroup) {
        this.collectFormErrors(control, fieldName, errors);
      } else if (control && control.invalid) {
        // Pular o campo federationUnitId que é apenas auxiliar
        if (fieldName === 'address.federationUnitId') {
          return;
        }

        const fieldLabel = this.getFieldLabel(fieldName);
        const errorMessages = this.getErrorMessages(control.errors);
        errors.push(`${fieldLabel}: ${errorMessages}`);
      }
    });
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      'name': 'Nome',
      'cpf': 'CPF',
      'positionTypeId': 'Tipo de Cargo',
      'address.addressTypeId': 'Tipo de Endereço',
      'address.street': 'Logradouro',
      'address.number': 'Número',
      'address.neighborhood': 'Bairro',
      'address.postalCode': 'CEP',
      'address.federationUnitId': 'Estado',
      'address.cityId': 'Cidade',
      'phone.phoneTypeId': 'Tipo de Telefone',
      'phone.areaCodeId': 'DDD',
      'phone.number': 'Número do Telefone',
      'email.emailTypeId': 'Tipo de Email',
      'email.emailAddress': 'Endereço de Email'
    };

    return labels[fieldName] || fieldName;
  }

  private getErrorMessages(errors: any): string {
    if (!errors) return '';

    const messages: string[] = [];

    if (errors.required) messages.push('Campo obrigatório');
    if (errors.cpfInvalid) messages.push('CPF inválido');
    if (errors.emailInvalid) messages.push('Email inválido');
    if (errors.mask) messages.push('Formato inválido');

    return messages.join(', ') || 'Erro de validação';
  }

  private updateFormValuesFromComponents(formGroup: FormGroup): void {
    // Força a atualização dos valores dos componentes vlo
    // Isso é necessário porque os componentes vlo podem não estar sincronizados

    // Primeiro, vamos sincronizar os valores dos elementos DOM com os FormControls
    this.syncDOMValuesWithFormControls(formGroup);

    // Marcar todos os campos como touched para mostrar erros de validação
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.updateFormValuesFromComponents(control);
      } else {
        control?.markAsTouched();
        control?.markAsDirty();
      }
    });

    // Forçar uma nova validação após marcar como touched
    formGroup.updateValueAndValidity();
  }

  private syncDOMValuesWithFormControls(formGroup: FormGroup, prefix: string = ''): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      const fieldPath = prefix ? `${prefix}.${key}` : key;

      if (control instanceof FormGroup) {
        this.syncDOMValuesWithFormControls(control, fieldPath);
      } else {
        // Tentar encontrar o elemento DOM correspondente e sincronizar o valor
        const inputElement = document.querySelector(`input[name="${key}"], select[name="${key}"]`) as HTMLInputElement | HTMLSelectElement;
        if (inputElement && inputElement.value && inputElement.value !== control?.value) {
          control?.setValue(inputElement.value);
        }
      }
    });
  }
}
