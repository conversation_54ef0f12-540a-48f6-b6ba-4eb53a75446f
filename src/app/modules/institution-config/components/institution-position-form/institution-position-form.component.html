<div class="institution-position-form">
  <form [formGroup]="form" novalidate>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-7">
      <div>
        <h4 class="fw-bold text-dark">Cargos da Instituição</h4>
        <div class="text-muted fs-6">
          <PERSON><PERSON><PERSON><PERSON> as pessoas que ocupam cargos na instituição
        </div>
      </div>
      <button type="button"
              class="btn btn-primary"
              (click)="onAddPosition()">
        <i class="ki-duotone ki-plus fs-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Adicionar Cargo
      </button>
    </div>

    <!-- Lista de Cargos -->
    <div formArrayName="positions">
      <div class="position-card mb-7"
           *ngFor="let position of positionsFormArray.controls; let i = index"
           [formGroupName]="i">

        <!-- Header do Cargo -->
        <div class="card">
          <div class="card-header">
            <div class="card-title">
              <h5 class="fw-bold m-0">
                <i class="ki-duotone ki-profile-user fs-2 me-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
                {{ getPositionFormGroup(i).get('name')?.value || 'Novo Cargo' }}
              </h5>
            </div>

            <div class="card-toolbar">
              <div class="d-flex gap-2">
                <button type="button"
                        class="btn btn-sm btn-outline-info"
                        (click)="toggleDebug()">
                  <i class="ki-duotone ki-code fs-7 me-1">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                  </i>
                  {{ showDebug ? 'Ocultar' : 'Mostrar' }} Debug
                </button>

                <button type="button"
                        class="btn btn-sm btn-primary"
                        [disabled]="saving"
                        (click)="onSavePosition(i)">
                  <i class="ki-duotone ki-check fs-6 me-2">
                    <span class="path1"></span>
                    <span class="path2"></span>
                  </i>
                  Adicionar
                </button>

                <button type="button"
                        class="btn btn-sm btn-danger"
                        (click)="onRemovePosition(i)">
                  <i class="ki-duotone ki-trash fs-6 me-2">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                    <span class="path5"></span>
                  </i>
                  Remover
                </button>
              </div>
            </div>
          </div>

          <div class="card-body">
            <!-- Debug Visual -->
            <div *ngIf="showDebug" class="alert alert-warning mb-6">
              <h6><i class="ki-duotone ki-code fs-2 me-2">
                <span class="path1"></span>
                <span class="path2"></span>
                <span class="path3"></span>
                <span class="path4"></span>
              </i> Debug do Formulário - Cargo {{ i + 1 }}</h6>
              <div class="row">
                <div class="col-md-4">
                  <strong>Status Geral:</strong>
                  <span class="badge" [ngClass]="getPositionFormGroup(i).valid ? 'badge-success' : 'badge-danger'">
                    {{ getPositionFormGroup(i).valid ? 'Válido' : 'Inválido' }}
                  </span><br>
                  <strong>Touched:</strong> {{ getPositionFormGroup(i).touched ? 'Sim' : 'Não' }}<br>
                  <strong>Dirty:</strong> {{ getPositionFormGroup(i).dirty ? 'Sim' : 'Não' }}
                </div>
                <div class="col-md-8">
                  <strong>Campos Inválidos:</strong>
                  <div *ngIf="getFormErrors(i).length === 0" class="text-success">
                    <i class="ki-duotone ki-check fs-2"></i> Todos os campos estão válidos!
                  </div>
                  <ul *ngIf="getFormErrors(i).length > 0" class="mb-0">
                    <li *ngFor="let error of getFormErrors(i)" class="text-danger">
                      <i class="ki-duotone ki-cross fs-7 me-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Tabs para organizar os dados -->
            <vlo-tabs [navClass]="'nav-line-tabs-2x'">

              <!-- Tab: Dados Pessoais -->
              <vlo-tab tabTitle="Dados Pessoais">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-7">
                      <vlo-select
                        label="Tipo de Cargo"
                        formControlName="positionTypeId"
                        placeholder="Selecione o cargo"
                        [options]="positionTypeOptions"
                        [required]="true">
                      </vlo-select>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="mb-7">
                      <label class="form-label required">Nome Completo</label>
                      <input
                        type="text"
                        class="form-control"
                        formControlName="name"
                        placeholder="Digite o nome completo"
                        [class.is-invalid]="isPositionFieldInvalid(i, 'name')">
                      <div class="invalid-feedback" *ngIf="isPositionFieldInvalid(i, 'name')">
                        {{ getPositionFieldError(i, 'name') }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4">
                    <div class="mb-7">
                      <label class="form-label required">CPF</label>
                      <input
                        type="text"
                        class="form-control"
                        formControlName="cpf"
                        placeholder="000.000.000-00"
                        mask="000.000.000-00"
                        [class.is-invalid]="isPositionFieldInvalid(i, 'cpf')">
                      <div class="invalid-feedback" *ngIf="isPositionFieldInvalid(i, 'cpf')">
                        {{ getPositionFieldError(i, 'cpf') }}
                      </div>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="mb-7">
                      <vlo-input
                        label="Data de Nascimento"
                        type="date"
                        formControlName="birthDate"
                        placeholder="dd/mm/aaaa">
                      </vlo-input>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="mb-7">
                      <vlo-select
                        label="Estado Civil"
                        formControlName="maritalStatus"
                        placeholder="Selecione"
                        [options]="maritalStatuses">
                      </vlo-select>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12">
                    <div class="mb-7">
                      <vlo-input
                        label="Profissão"
                        formControlName="profession"
                        placeholder="Digite a profissão">
                      </vlo-input>
                    </div>
                  </div>
                </div>
              </vlo-tab>

              <!-- Tab: Endereço -->
              <vlo-tab tabTitle="Endereço">
                <div formGroupName="address">
                <div class="row">

                  <div class="col-md-3">
                    <div class="mb-7">
                      <vlo-select
                        label="Tipo de Endereço"
                        formControlName="addressTypeId"
                        placeholder="Selecione o tipo"
                        [options]="addressTypeOptions"
                        [required]="true">
                      </vlo-select>
                    </div>
                  </div>

                  <div class="col-md-5">
                    <div class="mb-7">
                      <label class="form-label required">Logradouro</label>
                      <input
                        type="text"
                        class="form-control"
                        formControlName="street"
                        placeholder="Digite o nome da rua"
                        [class.is-invalid]="isPositionFieldInvalid(i, 'address.street')">
                      <div class="invalid-feedback" *ngIf="isPositionFieldInvalid(i, 'address.street')">
                        {{ getPositionFieldError(i, 'address.street') }}
                      </div>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="mb-7">
                      <vlo-input
                        label="Número"
                        formControlName="number"
                        placeholder="123">
                      </vlo-input>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-7">
                      <vlo-input
                        label="Complemento"
                        formControlName="complement"
                        placeholder="Apto, Bloco, etc.">
                      </vlo-input>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="mb-7">
                      <vlo-input
                        label="Bairro"
                        formControlName="neighborhood"
                        placeholder="Digite o bairro">
                      </vlo-input>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-3">
                    <div class="mb-7">
                      <vlo-input
                        label="CEP"
                        formControlName="postalCode"
                        placeholder="00000-000"
                        mask="00000-000">
                      </vlo-input>
                    </div>
                  </div>

                  <div class="col-md-3">
                    <div class="mb-7">
                      <vlo-select
                        label="Estado"
                        formControlName="federationUnitId"
                        placeholder="Selecione o estado"
                        [options]="federationUnitOptions"
                        [required]="true"
                        (selectionChange)="onFederationUnitChange($event, i)">
                      </vlo-select>
                    </div>
                  </div>

                  <div class="col-md-3">
                    <div class="mb-7">
                      <vlo-select
                        label="Cidade"
                        formControlName="cityId"
                        placeholder="Selecione a cidade"
                        [options]="cityOptions"
                        [required]="true">
                      </vlo-select>
                    </div>
                  </div>
                </div>
                </div>
              </vlo-tab>

              <!-- Tab: Contato -->
              <vlo-tab tabTitle="Contato">
                <div class="row">
                  <!-- Telefone -->
                  <div class="col-md-6" formGroupName="phone">
                    <h6 class="fw-bold text-dark mb-4">
                      <i class="ki-duotone ki-phone fs-3 me-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                      Telefone
                    </h6>

                    <div class="row">
                      <div class="col-4">
                        <div class="mb-7">
                          <vlo-select
                            label="DDD"
                            formControlName="areaCodeId"
                            placeholder="DDD"
                            [options]="areaCodeOptions"
                            [required]="true">
                          </vlo-select>
                        </div>
                      </div>

                      <div class="col-8">
                        <div class="mb-7">
                          <label class="form-label required">Número do Telefone</label>
                          <input
                            type="text"
                            class="form-control"
                            formControlName="number"
                            placeholder="99999-9999"
                            mask="00000-0000"
                            [class.is-invalid]="isPositionFieldInvalid(i, 'phone.number')">
                          <div class="invalid-feedback" *ngIf="isPositionFieldInvalid(i, 'phone.number')">
                            {{ getPositionFieldError(i, 'phone.number') }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-7">
                      <vlo-select
                        label="Tipo de Telefone"
                        formControlName="phoneTypeId"
                        placeholder="Selecione o tipo"
                        [options]="phoneTypeOptions"
                        [required]="true">
                      </vlo-select>
                    </div>
                  </div>

                  <!-- Email -->
                  <div class="col-md-6" formGroupName="email">
                    <h6 class="fw-bold text-dark mb-4">
                      <i class="ki-duotone ki-sms fs-3 me-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                      Email
                    </h6>

                    <div class="mb-7">
                      <label class="form-label required">Endereço de Email</label>
                      <input
                        type="email"
                        class="form-control"
                        formControlName="emailAddress"
                        placeholder="<EMAIL>"
                        [class.is-invalid]="isPositionFieldInvalid(i, 'email.emailAddress')">
                      <div class="invalid-feedback" *ngIf="isPositionFieldInvalid(i, 'email.emailAddress')">
                        {{ getPositionFieldError(i, 'email.emailAddress') }}
                      </div>
                    </div>

                    <div class="mb-7">
                      <vlo-select
                        label="Tipo de Email"
                        formControlName="emailTypeId"
                        placeholder="Selecione o tipo"
                        [options]="emailTypeOptions"
                        [required]="true">
                      </vlo-select>
                    </div>
                  </div>
                </div>
              </vlo-tab>

            </vlo-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div class="text-center py-10" *ngIf="positionsFormArray.length === 0">
      <div class="mb-5">
        <i class="ki-duotone ki-people fs-5x text-muted">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
          <span class="path4"></span>
          <span class="path5"></span>
        </i>
      </div>
      <h4 class="text-muted">Nenhum cargo cadastrado</h4>
      <p class="text-muted">Clique em "Adicionar Cargo" para começar a cadastrar os cargos da instituição.</p>
    </div>

  </form>
</div>
