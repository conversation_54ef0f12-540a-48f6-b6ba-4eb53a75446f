<div class="d-flex justify-content-between align-items-center mb-5">
  <div>
    <h1 class="fw-bold">{{ isEditMode ? 'Editar' : 'Nova' }} Configuração de Instituição</h1>
    <p class="text-muted mb-0">
      {{ isEditMode ? 'Atualize as informações' : 'Configure os dados' }} da instituição
    </p>
  </div>

  <div class="d-flex gap-2">
    <vlo-button
      text="Cancelar"
      type="secondary"
      (click)="onCancel()">
    </vlo-button>

    <vlo-button
      text="Salvar"
      icon="bi-pencil-fill"
      iconType="bootstrap"
      type="primary"
      [disabled]="saving"
      (click)="onSave()">
    </vlo-button>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="text-center py-5">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Carregando...</span>
  </div>
  <p class="mt-3 text-muted">Carregando dados...</p>
</div>

<!-- Formulário com Abas VLO -->
<div *ngIf="!loading">
  <form [formGroup]="form" (ngSubmit)="onSave()">

    <vlo-tabs [navClass]="'nav-line-tabs-2x'">

      <!-- Aba Configurações Gerais -->
      <vlo-tab tabTitle="Configurações Gerais">

        <div class="card p-10">
          <div class="row">
            <!-- Nome da UEL -->
            <div class="col-md-8">
              <div class="mb-4">
                <vlo-input
                  label="Nome da UEL"
                  formControlName="uelName"
                  placeholder="Digite o nome da UEL"
                  [required]="true">
                </vlo-input>
                <div class="form-text">
                  <i class="bi bi-info-circle me-1"></i>
                  Nome oficial da Unidade Escoteira Local
                </div>
              </div>
            </div>
          </div>

          <!-- Upload do Logo -->
          <div class="row">
            <div class="col-12">
              <div class="mb-4">
                <app-image-upload
                  label="Logo da Instituição"
                  placeholder="Clique para selecionar o logo"
                  formControlName="logoS3Key"
                  uploadType="institution-logo"
                  [maxSizeMB]="5"
                  [required]="false"
                  previewWidth="200px"
                  previewHeight="120px"
                  [institutionConfigId]="institutionConfigId"
                  (uploadComplete)="onLogoUploaded($event)"
                  (uploadError)="onLogoUploadError($event)">
                </app-image-upload>
                <div class="form-text">
                  <i class="bi bi-info-circle me-1"></i>
                  Formatos aceitos: PNG, JPEG. Tamanho máximo: 5MB. Recomendado: 400x240px
                </div>
              </div>
            </div>
          </div>
        </div>

      </vlo-tab>

      <!-- Aba Configuração SMTP -->
      <vlo-tab tabTitle="Configuração SMTP">

        <div class="card p-10">
          <!-- Usar o componente SMTP dedicado -->
          <app-smtp-config-form
            formControlName="smtpConfig"
            [institutionConfigId]="institutionConfigId">
          </app-smtp-config-form>
        </div>

      </vlo-tab>

      <!-- Aba Cargos -->
      <vlo-tab tabTitle="Cargos">
        <app-institution-position-form
          [institutionConfigId]="institutionConfigId"
          [positions]="positions"
          [positionTypes]="positionTypes">
        </app-institution-position-form>
      </vlo-tab>

    </vlo-tabs>

  </form>
</div>


