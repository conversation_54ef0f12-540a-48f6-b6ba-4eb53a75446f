import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

// Educatena Components
import { TableModule, FormModule, SecurityModule } from '@educatena/edu-web';

// Shared Module
import { SharedModule } from '../../shared/shared.module';

// Components
import { InstitutionConfigListComponent } from './components/institution-config-list/institution-config-list.component';
import { InstitutionConfigFormComponent } from './components/institution-config-form/institution-config-form.component';
import { InstitutionPositionFormComponent } from './components/institution-position-form/institution-position-form.component';
import { SmtpConfigFormComponent } from './components/smtp-config-form/smtp-config-form.component';
import { PositionTypeListComponent } from './components/position-type-list/position-type-list.component';
import { PositionTypeFormComponent } from './components/position-type-form/position-type-form.component';

// Services
import { InstitutionConfigService } from '../../services/institution-config.service';

const routes = [
  {
    path: '',
    component: InstitutionConfigListComponent
  },
  {
    path: 'new',
    component: InstitutionConfigFormComponent
  },
  {
    path: 'edit/:id',
    component: InstitutionConfigFormComponent
  },
  {
    path: 'position-types',
    component: PositionTypeListComponent
  },
  {
    path: 'position-types/new',
    component: PositionTypeFormComponent
  },
  {
    path: 'position-types/edit/:id',
    component: PositionTypeFormComponent
  }
];

@NgModule({
  declarations: [
    InstitutionConfigListComponent,
    InstitutionConfigFormComponent,
    InstitutionPositionFormComponent,
    SmtpConfigFormComponent,
    PositionTypeListComponent,
    PositionTypeFormComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule.forChild(routes),
    NgbModule,

    // Shared Module
    SharedModule,

    // Educatena Components
    TableModule,
    FormModule,
    SecurityModule
  ],
  providers: [
    InstitutionConfigService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class InstitutionConfigModule { }
