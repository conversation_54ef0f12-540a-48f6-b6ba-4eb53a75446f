export interface Extrato {
  dataTransacao: Date;
  dataTransacaoFmt: string;
  dataTransacaoFmtMes: string;
  dataTransacaoStr: string;
  descTransacao: string;
  functionCode: string;
  descFunctionCode: string;
  descLocal: string;
  hasComprovante: boolean;
  idTranlog: number;
  idTransacao: string;
  isTransacaoPontos: boolean;
  nsu: string;
  quatroUltimosNumeros: string;
  sinal: number;
  statusTransacao: string;
  transacaoEstornada: boolean;
  transacaoEstorno: boolean;
  valorConversao: number;
  valorPontosEmReais: number;
  valorTransacao: number;
  valordoPonto: number;
  descTransacaoMinima: string;
  ss: string;
  possuiComprovante: boolean;
  idContaOrigem: number;
}

export interface Transacao {
  dataFmt: string;
  data: Date;
  operacoes: Extrato[];
}
