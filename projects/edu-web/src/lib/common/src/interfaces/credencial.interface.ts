import { Conta } from './conta.interface';

export interface Credencial {
  credencialMascarada: string;
  credencialMascaradaReduzida: string;
  credencialUltimosDigitos: string;
  nomeProduto: string;
  tipoConta: number;
  saldo: number;
  idProduto: number;
  dataValidade: string;
  contaPagamento: string;
  idCredencial: number;
  idConta: number;
  idPessoa: number;
  nomeImpresso: string;
  nomeCompleto: string;
  dataValidadeFmt: string;
  dataHoraInclusao: string;
  binEstendido: number;
  status: number;
  statusNfc: number;
  descStatus: string;
  grupoStatus: number;
  descGrupoStatus: string;
  idPlastico: number;
  idProdutoPlataforma: number;
  dataSaldo: string;
  email: string;
  permiteInibirFatura: string;
  faturaInibida: string;
  idProdutoInstituicao: number;
  preparaDataSaldo: string;
  codigoSeguranca: string;
  apelidoVirtual: string;
  credencialVirtual: string;
  mesesValidadeCartaoVitual: number;
  metodoSegurancaTransacao: number;
  virtual: boolean;
  contas: Conta[];
  permiteCartaoFisico: boolean;
  removerListaInicial?: boolean;
  senhaAlterada: boolean;
  pontoRelacionamento: number;
  descPontoRelacionamento: string;
  dataHoraEmitido: string;
  bin: number;
  valorMinCarga: number;
  valorMaxCarga: number;
}
