export interface ConsultaQrCodePix {
  transactionId: string;
  erroMessage: ErroMessage;
  value: Value;
  isSuccess: boolean;
  isFailure: boolean;
}

interface ErroMessage {
  statusCode: number;
  errors: any[];
}

interface DocumentoCobranca {
  revisao: string;
}

interface Calendario {
  apresentacao: Date;
  criacao: Date;
  vencimento: Date;
  validadeAposVencimento: number;
}

interface Pagador {
  inscricaoNacional: string;
  nome: string;
}

interface Tipo {
  id: string;
  descricao: string;
}

interface Pessoa {
  tipo: Tipo;
  inscricaoNacional: string;
  nome: string;
  logradouro?: string;
  cidade: string;
  uf?: string;
  cep?: string;
}

interface ContaCalendario {
  dataCriacao: Date;
  dataPosse: Date;
  dataAbertura: Date;
}

interface Conta {
  nomePsp: string;
  ispbParticipante: string;
  tipo: number;
  agencia: string;
  numero: string;
  calendario: ContaCalendario;
}

interface Beneficiario {
  tipoChave: string;
  chave: string;
  endToEnd: string;
  pessoa: Pessoa;
  conta: Conta;
}

export interface Valor {
  original: number;
  final: number;
  juros: number;
  multa: number;
  desconto: number;
  abatimento: number;
}

interface InfoAdicional {
  nome: string;
  valor: string;
}

interface Value {
  nome: string;
  documentoCobranca: DocumentoCobranca;
  calendario: Calendario;
  pagador: Pagador;
  beneficiario: Beneficiario;
  valor: Valor | number;
  info: string;
  referencia: string;
  infoAdicional: InfoAdicional[];
  tipoQRCode: 'ESTATICO' | 'DINAMICO';
  tipoPix: string;
  descricao?: string;
  identificador?: string;
  nomeQRCode?: string;
}

