import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'issuer',
  templateUrl: './issuer.component.html',
  styleUrl: './issuer.component.css'
})
export class IssuerComponent implements OnInit {
  @Input() url: string;
  safeUrl: SafeResourceUrl;

  constructor(private sanitizer: DomSanitizer) {}

  ngOnInit() {

    const params: any = localStorage.getItem('paramsIssuer');

    const paramsBase64 = btoa(params);

    this.url = `http://localhost:5555/login-unico/${paramsBase64}`;

    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.url);
  }

  stringTobase64(obj: any) {
    const jsonString = JSON.stringify(obj);
    return btoa(jsonString);
  }
}
