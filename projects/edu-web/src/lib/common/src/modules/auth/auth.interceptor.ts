import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';
import { TokenStorageService } from '../../services/token-storage.service';
import { EduConfigService } from '../config/edu-config.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private tokenStorage: TokenStorageService,
    private router: Router,
    private eduConfigService: EduConfigService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const authToken = this.tokenStorage.getToken();
    if (authToken) {
      const cloned = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${authToken}`),
        withCredentials: true
      });
      return next.handle(cloned).pipe(
        catchError((error: HttpErrorResponse) => this.handleError(error))
      );
    }

    const authTokenPortador = this.tokenStorage.getTokenPortador();
    if (authTokenPortador) {
      const cloned = req.clone({
        headers: req.headers.set('AuthorizationPortador', `Bearer ${authTokenPortador}`),
        withCredentials: true
      });
      return next.handle(cloned).pipe(
        catchError((error: HttpErrorResponse) => this.handleError(error))
      );
    }

    const cloned = req.clone({
      withCredentials: true
    });
    return next.handle(cloned).pipe(
      catchError((error: HttpErrorResponse) => this.handleError(error))
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    console.log('error', error);

    if (error.status === 401) {
      const loginRoute = this.eduConfigService.auth?.loginRoute || '/auth/login';
      const currentUrl = this.router.url;

      // Se já estiver na rota de login, não interceptar o erro - deixar o app tratar
      if (currentUrl === loginRoute) {
        return throwError(error);
      }

      // Limpar tokens do storage
      this.tokenStorage.removeToken();

      // Mostrar aviso antes de redirecionar
      Swal.fire({
        title: 'Sessão Expirada',
        text: 'Sua sessão expirou. Você será redirecionado para a tela de login.',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#3085d6'
      }).then(() => {
        // Redirecionar para a tela de login configurada
        this.router.navigate([loginRoute]);
      });
    }

    return throwError(error);
  }
}
