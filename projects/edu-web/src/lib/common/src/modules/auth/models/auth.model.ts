export class AuthModel {
  token: string;
  refreshToken: string;
  expiresIn: Date;

  setAuth(auth: AuthModel) {
    this.token = auth.token;
    this.refreshToken = auth.refreshToken;
    this.expiresIn = auth.expiresIn;
  }
}

export class PortadorAuthModel {
  cpf: string;
  documentoAcesso: string;
  encaminharAtendimento: boolean;
  idLogin: number;
  isAntifraudeValidacaoNecessaria: boolean;
  isDeviceIdValido: boolean;
  limitesPIXAlterados: boolean;
  onboardRealizado: boolean;
  token: string;

  setAuth(auth: PortadorAuthModel) {
    this.cpf = auth.cpf;
    this.documentoAcesso = auth.documentoAcesso;
    this.encaminharAtendimento = auth.encaminharAtendimento;
    this.idLogin = auth.idLogin;
    this.isAntifraudeValidacaoNecessaria = auth.isAntifraudeValidacaoNecessaria;
    this.isDeviceIdValido = auth.isDeviceIdValido;
    this.limitesPIXAlterados = auth.limitesPIXAlterados;
    this.onboardRealizado = auth.onboardRealizado;
    this.token = auth.token;
  }
}
