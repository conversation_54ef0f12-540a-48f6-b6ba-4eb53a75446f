import { Component, OnDestroy, OnInit } from '@angular/core';
import { EduConfigService } from '../config';

// const BODY_CLASSES = ['bgi-size-cover', 'bgi-position-center', 'bgi-no-repeat'];

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '<body[root]>',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
})
export class AuthComponent implements OnInit, OnDestroy {
  today: Date = new Date();
  logo: string | undefined;
  illustration: string | undefined;
  title: string | undefined;
  subtitle: string | undefined;
  link: string | undefined = '#';
  linkText: string | undefined;

  constructor(private configService: EduConfigService) {
    console.log(this.configService);
    this.logo = this.configService.auth?.authLogo
    this.illustration = this.configService.auth?.authIllustration
    this.title = this.configService.auth?.authTitle
    this.subtitle = this.configService.auth?.authSubtitle
    this.link = this.configService.auth?.authLink
    this.linkText = this.configService.auth?.authLinkText
  }

  ngOnInit(): void {
    // BODY_CLASSES.forEach((c) => document.body.classList.add(c));
  }

  ngOnDestroy() {
    // BODY_CLASSES.forEach((c) => document.body.classList.remove(c));
  }
}
