import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { TokenStorageService } from '../../../../services/token-storage.service';
import { UserModel } from '../../models/user.model';
// import { environment } from '../../../../../environments/environment';
import { AuthModel } from '../../models/auth.model';
import { EduConfigService } from '../../../config';
import { AuthRequest } from '../../models/auth-request.model';
import { Usuario } from '../../../../interfaces/usuario.interface';
import { Util } from '../../../../util/Util';
import { StorageService } from '../../../../services/storage.service';

@Injectable({
  providedIn: 'root',
})
export class AuthHTTPService {
  private usuario$: BehaviorSubject<Usuario> = new BehaviorSubject<Usuario>({} as Usuario);

  private url: string = '';

  constructor(
    private http: HttpClient,
    private configService: EduConfigService,
    private tokenStorage: TokenStorageService,
    @Inject(StorageService) private storageService: StorageService
  ) {
    this.url = `${configService.baseUrl}/authenticate`;
  }

  // public methods
  login(email: string, password: string | null = null): Observable<any> {
    return this.http.post<AuthModel>(this.url, {
      email,
      password,
    });
  }

  getIssuerPermissions(): Observable<any> {
    const token = this.tokenStorage.getTokenIssuer();
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.get<any>(
      `${this.configService.config.baseUrl}/api/usuario/permissions`,
      { headers, withCredentials: true }
    );
  }

  // CREATE =>  POST: add a new user to the server
  createUser(user: UserModel): Observable<UserModel> {
    return this.http.post<UserModel>(this.url, user);
  }

  // Your server should check email => If email exists send link to the user and return true | If email doesn't exist return false
  forgotPassword(email: string): Observable<boolean> {
    return this.http.post<boolean>(`${this.url}/forgot-password`, {
      email,
    });
  }

  getUserByToken(token: string): Observable<UserModel> {
    const httpHeaders = new HttpHeaders({
      Authorization: `Bearer ${token}`,
    });
    return this.http.get<UserModel>(`${this.url}/me`, {
      headers: httpHeaders,
    });
  }

  loginBase(baseUrl: string | null, params: any): Observable<any> {
    const finalBaseUrl = baseUrl || this.configService.config.baseUrl;
    const url = `${finalBaseUrl}/api/login-unico/token`;
    return this.http.post(url, params, { withCredentials: true });
  }

  private setCredenciais(credenciais: any[]) {
    const usuario: Usuario = this.getUser();
    const cartoes = [];
    for (const item of credenciais) {
      const cartao = item.credencial;
      cartao.credencialMascarada = cartao.credencialMascarada.replaceAll('X', '●').replaceAll('-', ' ');
      cartao.contas = item.listaContaPagamentos.map((x: any) => {
        x.digitoVerificador = Util.calcularDigitoVerificadorConta(x.idConta);
        x.agencia = x.idInstituicao;
        return x;
      });
      cartoes.push(cartao);
    }
    usuario.credenciais = cartoes;
    this.storageService.setUser(usuario);
  }

  getUser(): Usuario {
    const user = this.storageService.getUser();
    this.usuario$.next(user);
    return user;
  }
}
