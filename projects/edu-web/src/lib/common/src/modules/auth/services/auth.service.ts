import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { Observable, BehaviorSubject, of, Subscription } from 'rxjs';
import { map, catchError, switchMap, finalize } from 'rxjs/operators';
import { PortadorUserModel, UserModel } from '../models/user.model';
import { AuthModel, PortadorAuthModel } from '../models/auth.model';
import { AuthHTTPService } from './auth-http';
import { Router } from '@angular/router';
import { EduConfigService } from '../../config';
import { TokenStorageService } from '../../../services/token-storage.service';
import { AccessControlService } from '../../../services/access-control.service';
import { AuthRequest } from '../models/auth-request.model';
import { StorageService } from '../../../services/storage.service';

export type UserType = UserModel | PortadorUserModel |  undefined;

@Injectable({
  providedIn: 'root',
})
export class AuthService implements OnDestroy {
  private unsubscribe: Subscription[] = [];

  currentUser$: Observable<UserType>;
  isLoading$: Observable<boolean>;
  currentUserSubject: BehaviorSubject<UserType>;
  isLoadingSubject: BehaviorSubject<boolean>;

  private pinCredencial = '';

  get currentUserValue(): UserType {
    return this.currentUserSubject.value;
  }

  set currentUserValue(user: UserType) {
    this.currentUserSubject.next(user);
  }

  constructor(
    private authHttpService: AuthHTTPService,
    private router: Router,
    private tokenStorage: TokenStorageService,
    private storageService: StorageService,
    private accessControlService: AccessControlService,
    private configService: EduConfigService
  ) {
    this.isLoadingSubject = new BehaviorSubject<boolean>(false);
    this.currentUserSubject = new BehaviorSubject<UserType>(undefined);
    this.currentUser$ = this.currentUserSubject.asObservable();
    this.isLoading$ = this.isLoadingSubject.asObservable();

    this.reconstructCurrentUser();

    const subscr = this.getUserByToken().subscribe();
    this.unsubscribe.push(subscr);
  }

  setPinCredencial(pinCredencial: string) {
    this.pinCredencial = pinCredencial;
  }

  getPinCredencial() {
    return this.pinCredencial;
  }

  login(email: string, password: string): Observable<UserType> {
    this.isLoadingSubject.next(true);
    return this.authHttpService.login(email, password).pipe(
      map((auth: AuthModel) => {
        this.tokenStorage.saveToken(auth);
        return auth;
      }),
      switchMap((auth: AuthModel) => this.authHttpService.getUserByToken(auth.token)),
      map((user: UserModel) => {
        user.token = this.tokenStorage.getToken() || '';
        this.currentUserValue = user;
        return user;
      }),
      catchError((err) => {
        console.error('err', err);
        return of(undefined);
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }

  getIssuerPermissions(): Observable<AuthModel> {
    this.isLoadingSubject.next(true);
    return this.authHttpService.getIssuerPermissions().pipe(
      map((permissions: any) => {
        this.tokenStorage.savePermissionsIssuer(permissions);
        return permissions;
      }),
      catchError((err) => {
        console.error('Erro ao obter as permissões do issuer:', err);
        throw err;  // Repassa o erro ou retorna um valor padrão
      }),
      finalize(() => this.isLoadingSubject.next(false))  // Finaliza e desativa o indicador de loading
    );
  }

  logout() {
    this.tokenStorage.removeToken();
    this.currentUserSubject.next(undefined);

    const loginRoute = this.configService.auth?.loginRoute || '/auth/login';
    this.router.navigate([loginRoute], {
      queryParams: {},
    });
  }

  getUserByToken(): Observable<UserType> {

    const token = this.tokenStorage.getToken();
    if (!token) {
      return of(undefined);
    }

    this.isLoadingSubject.next(true);
    return this.authHttpService.getUserByToken(token).pipe(
      map((response: any) => {
        const user: UserType = response;
        // Extract all the groups user permissions
        const permissions = response.permissions;

        // Initializing the AccessControlService with the permissions
        this.accessControlService.initialize(permissions!);

        if (user) {
          user.token = token;
          this.currentUserSubject.next(user);
        } else {
          this.logout();
        }
        return user;
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }

  registration(user: UserModel): Observable<any> {
    this.isLoadingSubject.next(true);
    return this.authHttpService.createUser(user).pipe(
      switchMap(() => this.login(user.email, user.password)),
      catchError((err) => {
        console.error('err', err);
        return of(undefined);
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }

  forgotPassword(email: string): Observable<boolean> {
    this.isLoadingSubject.next(true);
    return this.authHttpService
      .forgotPassword(email)
      .pipe(finalize(() => this.isLoadingSubject.next(false)));
  }

  loginBase(baseUrl: string, params: any): Observable<any> {
    this.isLoadingSubject.next(true);
    return this.authHttpService.loginBase(baseUrl, params).pipe(
      map((auth: AuthModel) => {
        this.tokenStorage.saveTokenIssuer(auth);
        localStorage.setItem('paramsIssuer', JSON.stringify(params));
        return auth;
      }),
      catchError((err) => {
        console.error('Erro durante o login base:', err);
        throw err;
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  private reconstructCurrentUser(): void {
    const user = this.tokenStorage.getCurrentUserFromToken() ||
                 this.tokenStorage.getCurrentUserFromTokenIssuer() ||
                 this.tokenStorage.getCurrentUserFromTokenPortador();

    if (user) {
      this.currentUserSubject.next(user);
    }
  }
}
