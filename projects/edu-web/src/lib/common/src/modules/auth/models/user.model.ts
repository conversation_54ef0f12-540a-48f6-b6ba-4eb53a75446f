import { AuthModel } from './auth.model';

export interface Permission {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface Group {
  id: number;
  name: string;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
}

export class UserModel extends AuthModel {
  id: number;
  username: string;
  password: string;
  fullname: string;
  email: string;
  pic: string;
  permissions: string[] = [];
  groups: Group[] = []; // Adiciona os grupos ao usuário
  // personal information
  firstname: string;
  lastname: string;

  setUser(_user: unknown) {
    const user = _user as UserModel;
    this.id = user.id;
    this.username = user.username || '';
    this.password = user.password || '';
    this.fullname = user.fullname || '';
    this.email = user.email || '';
    this.pic = user.pic || './assets/media/avatars/blank.png';
    this.permissions = user.permissions || [];

    // Inicializa os grupos
    this.groups = user.groups || [];
  }
}

export class PortadorUserModel extends AuthModel {
  cpf: string;
  documentoAcesso: string;
  idLogin: number;
  isDeviceIdValido: boolean;
  isAntifraudeValidacaoNecessaria: boolean;
  encaminharAtendimento: boolean;
  onboardRealizado: boolean;
  limitesPIXAlterados: boolean;

  setUser(_user: unknown) {
    const user = _user as PortadorUserModel;
    this.documentoAcesso = user.documentoAcesso;
    this.idLogin = user.idLogin;
    this.isDeviceIdValido = user.isDeviceIdValido;
    this.isAntifraudeValidacaoNecessaria = user.isAntifraudeValidacaoNecessaria;
    this.encaminharAtendimento = user.encaminharAtendimento;
    this.onboardRealizado = user.onboardRealizado;
    this.limitesPIXAlterados = user.limitesPIXAlterados;
  }
}
