import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from './auth.service';
import { EduConfigService } from '../../config';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
  constructor(
    private authService: AuthService,
    private router: Router,
    private configService: EduConfigService
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    console.log('canActivate...');
    const currentUser = this.authService.currentUserValue;
    const loginRoute = this.configService.auth?.loginRoute || '/auth/login';

    // Verifica se a rota atual é de logout ou login
    if (state.url.includes('/logout') || state.url.includes(loginRoute)) {
      // Se sim, apenas retorna true para prosseguir sem logout
      return true;
    }

    console.log('currentUser', currentUser);
    if (currentUser) {
      // logged in so return true
      return true;
    }

    // not logged in so redirect to login page with the return url
    this.authService.logout();
    return false;
  }
}
