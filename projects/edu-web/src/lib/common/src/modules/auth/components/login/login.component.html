<!--begin::Form-->
<form class="form w-full" [formGroup]="loginForm" novalidate="novalidate" id="kt_login_signin_form"
  (ngSubmit)="submit()">

  <!--begin::Heading-->
  <div class="text-center mb-11">
    <h1 class="text-gray-900 fw-bolder mb-3">
      Login
    </h1>
  </div>
  <!--end::Heading-->

  <!-- begin::Alert error-->
  <ng-container *ngIf="hasError">
    <div class="mb-lg-15 alert alert-danger">
      <div class="alert-text font-weight-bold">
        As informações de autenticação estão incorretas.
      </div>
    </div>
  </ng-container>
  <!-- end::Alert error-->

  <!--begin::Form group-->
  <div class="fv-row mb-8">
    <label class="form-label fs-6 fw-bolder text-gray-900">Email</label>
    <input class="form-control bg-transparent" type="email" name="email" formControlName="email" autocomplete="off"
      [ngClass]="{
        'is-invalid': loginForm.controls['email'].invalid,
        'is-valid': loginForm.controls['email'].valid
      }" />
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Email is required',
        control: loginForm.controls['email']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'email',
        message: 'Email is invalid',
        control: loginForm.controls['email']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minLength',
        message: 'Email should have at least 3 symbols',
        control: loginForm.controls['email']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Email should have maximum 360 symbols',
        control: loginForm.controls['email']
      }"></ng-container>
  </div>
  <!--end::Form group-->

  <!--begin::Form group-->
  <div class="fv-row mb-3">

    <label class="form-label fw-bolder text-gray-900 fs-6 mb-0">Password</label>

    <input class="form-control bg-transparent" type="password" name="password" autocomplete="off"
      formControlName="password" [ngClass]="{
        'is-invalid': loginForm.controls['password'].invalid,
        'is-valid': loginForm.controls['password'].valid
      }" />
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Password is required',
        control: loginForm.controls['password']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minlength',
        message: 'Password should have at least 3 symbols',
        control: loginForm.controls['password']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Password should have maximum 100 symbols',
        control: loginForm.controls['password']
      }"></ng-container>

  </div>
  <!--end::Form group-->

  <!--begin::Wrapper-->
  <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
    <div></div>

    <!--begin::Link-->
    <a routerLink="/auth/forgot-password" class="link-primary">
      Esqueceu a senha?
    </a>
    <!--end::Link-->
  </div>
  <!--end::Wrapper-->

  <!--begin::Action-->
  <div class="d-grid mb-10">
    <button type="submit" id="kt_sign_in_submit" class="btn btn-primary" [disabled]="loginForm.invalid">
      <ng-container *ngIf="isLoading$ | async">
        <span class="indicator-progress" [style.display]="'block'">
          Por favor aguarde...
          <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </span>
      </ng-container>
      <ng-container *ngIf="(isLoading$ | async) === false">
        <span class="indicator-label">Entrar</span>
      </ng-container>
    </button>
  </div>

  <!-- <div class="text-gray-500 text-center fw-semibold fs-6">
    Not a Member yet?

    <a routerLink="/auth/registration" class="link-primary">
      Sign up
    </a>
  </div> -->

  <!--end::Action-->
</form>
<!--end::Form-->

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
  <ng-container *ngIf="control.hasError(validation) && (control.dirty || control.touched)">
    <div class="fv-plugins-message-container">
      <span role="alert">
        {{ message }}
      </span>
    </div>
  </ng-container>
</ng-template>
