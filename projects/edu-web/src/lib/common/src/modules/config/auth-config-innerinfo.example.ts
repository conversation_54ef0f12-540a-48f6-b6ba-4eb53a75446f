/**
 * Exemplo de configuração do AuthConfig com innerInfo
 *
 * O innerInfo permite configurar qual informação do usuário será exibida
 * no dropdown do usuário no header da aplicação.
 */

import { AuthConfig } from './edu-config.service';

// Exemplo 1: Exibir CPF com label
const authConfigWithCpf: AuthConfig = {
  authLogo: 'assets/logo.png',
  authTitle: 'Minha Aplicação',
  loginRoute: '/auth/login',
  innerInfo: {
    label: 'CPF',
    field: 'cpf'
  }
};

// Exemplo 2: Exibir número de registro sem label
const authConfigWithRegistro: AuthConfig = {
  authLogo: 'assets/logo.png',
  authTitle: 'Minha Aplicação',
  loginRoute: '/auth/login',
  innerInfo: {
    label: '', // Sem label
    field: 'associado.numeroRegistroUEB'
  }
};

// Exemplo 3: Exibir matrícula com label personalizado
const authConfigWithMatricula: AuthConfig = {
  authLogo: 'assets/logo.png',
  authTitle: 'Minha Aplicação',
  loginRoute: '/auth/login',
  innerInfo: {
    label: 'Matrícula',
    field: 'funcionario.matricula'
  }
};

// Exemplo 4: Exibir código do usuário
const authConfigWithCodigo: AuthConfig = {
  authLogo: 'assets/logo.png',
  authTitle: 'Minha Aplicação',
  loginRoute: '/auth/login',
  innerInfo: {
    label: 'Código',
    field: 'codigo'
  }
};

/**
 * Estrutura do objeto user esperada:
 *
 * {
 *   cpf: "12345678901",
 *   codigo: "USR001",
 *   associado: {
 *     numeroRegistroUEB: "123456"
 *   },
 *   funcionario: {
 *     matricula: "F001"
 *   }
 * }
 */

/**
 * Resultados esperados no dropdown:
 *
 * Exemplo 1: "CPF: 123.456.789-01"
 * Exemplo 2: "123456"
 * Exemplo 3: "Matrícula: F001"
 * Exemplo 4: "Código: USR001"
 */
