import { NgModule, ModuleWithProviders } from '@angular/core';
import { EduConfigService, Config } from './edu-config.service';

@NgModule()
export class EduConfigModule {
  static config: EduConfigService = {
    baseUrl: '',
    config: { baseUrl: ''},
    appVersion: '0',
    appKey: '0',
    appTitle: '',
    smallLogo: ''
  };

  static forRoot(config: EduConfigService): ModuleWithProviders<EduConfigModule> {
    this.config = config;
    return {
      ngModule: EduConfigModule,
      providers: [
        {
          provide: EduConfigService,
          useValue: config
        }
      ]
    };
  }

  static configureMenuItems(menuItems: any): void {
    this.config.menuItems = menuItems;
  }
}
