import { Injectable } from '@angular/core';
import { MenuItem } from '../../models/menu-item';
import { Observable } from 'rxjs';

export class InnerInfo {
  label: string;
  field: string;
  format?: string;
}

export class AuthConfig {
  authLogo?: string;
  sidebarLogo?: string;
  authIllustration?: string;
  authTitle?: string;
  authSubtitle?: string;
  authLink?: string
  authLinkText?: string
  loginRoute?: string;
  innerInfo?: InnerInfo;
}

export interface Config {
  baseUrl: string;
}

@Injectable({
  providedIn: 'root'
})
export class EduConfigService {
  appTitle: string;
  baseUrl: string | null;
  config: Config;
  smallLogo: string;
  appVersion: string = '0';
  appKey?: string = '0';
  menuItems?: Observable<MenuItem[]>
  auth?: AuthConfig;
}
