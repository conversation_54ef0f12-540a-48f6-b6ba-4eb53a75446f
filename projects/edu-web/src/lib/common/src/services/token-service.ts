import { Injectable } from '@angular/core';
import { jwtDecode } from "jwt-decode";

@Injectable({
  providedIn: 'root',
})
export class TokenService {
  constructor() {}

  decodeToken(token: string): any {
    try {
      return jwtDecode(token);
    } catch (error) {
      console.error('Erro ao decodificar o token:', error);
      return null;
    }
  }

  getClaim(token: string, claim: string): any {
    const decodedToken = this.decodeToken(token);
    return decodedToken ? decodedToken[claim] : null;
  }
}
