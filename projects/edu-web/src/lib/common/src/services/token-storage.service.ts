import { Injectable } from '@angular/core';
import { AuthModel, PortadorAuthModel } from '../modules/auth/models/auth.model';
import { PortadorUserModel, UserModel } from '../modules/auth';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private authLocalStorageToken: string = 'app_user';
  private authLocalStorageTokenIssuer: string = 'app_user_issuer';
  private authLocalStorageTokenPortador: string = 'app_user_portador';
  private issuerPermissions: string = 'issuer_permisions';

  public saveToken(auth: AuthModel): void {
    if (auth && auth.token) {
      localStorage.setItem(this.authLocalStorageToken, JSON.stringify(auth));
    }
  }

  public saveTokenIssuer(auth: AuthModel): void {
    if (auth && auth.token) {
      localStorage.setItem(this.authLocalStorageTokenIssuer, JSON.stringify(auth));
    }
  }

  public saveTokenPortador(auth: AuthModel): void {
    if (auth && auth.token) {
      localStorage.setItem(this.authLocalStorageTokenPortador, JSON.stringify(auth));
    }
  }

  public savePermissionsIssuer(permissions: any): void {
    if (permissions && permissions.length > 0) {
      localStorage.setItem(this.issuerPermissions, JSON.stringify(permissions));
    }
  }

  public getToken(): string | null {
    const auth = this.getAuthFromLocalStorage();
    return auth ? auth.token : null;
  }

  public getUser() {
    const user: any = localStorage.getItem(this.authLocalStorageToken);
    return JSON.parse(user);
  }

  public removeToken(): void {
    console.log('removeToken...');

    localStorage.removeItem(this.authLocalStorageToken);
    localStorage.removeItem(this.authLocalStorageTokenIssuer);
    localStorage.removeItem(this.authLocalStorageTokenPortador);
  }

  public getTokenIssuer(): string | null {
    try {
      const auth = localStorage.getItem(this.authLocalStorageTokenIssuer);
      if (!auth) {
        return null;
      }
      const parsedAuth = JSON.parse(auth) as AuthModel;
      return parsedAuth.token || null;
    } catch (error) {
      console.error('Error getting issuer token:', error);
      return null;
    }
  }

  public getTokenPortador(): string | null {
    try {
      const auth = localStorage.getItem(this.authLocalStorageTokenPortador);
      if (!auth) {
        return null;
      }
      const parsedAuth = JSON.parse(auth) as AuthModel;
      return parsedAuth.token || null;
    } catch (error) {
      console.error('Error getting issuer token:', error);
      return null;
    }
  }

  private getAuthFromLocalStorage(): AuthModel | undefined {
    try {
      const lsValue = localStorage.getItem(this.authLocalStorageToken);
      if (!lsValue) {
        return undefined;
      }
      return JSON.parse(lsValue) as AuthModel;
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }

  public getCurrentUserFromToken(): UserModel | undefined {
    const auth = this.getAuthFromLocalStorage();
    return auth ? this.parseUserFromAuth(auth) : undefined;
  }

  public getCurrentUserFromTokenIssuer(): UserModel | undefined {
    const auth = this.getAuthFromLocalStorageIssuer();
    return auth ? this.parseUserFromAuth(auth) : undefined;
  }

  public getCurrentUserFromTokenPortador(): PortadorUserModel | undefined {
    const auth = this.getAuthFromLocalStoragePortador();
    return auth ? this.parsePortadorUserFromAuth(auth) : undefined;
  }

  private parseUserFromAuth(auth: any): UserModel {
    return {
      id: auth.id,
      username: auth.username,
      password: auth.password,
      fullname: auth.fullname,
      email: auth.email,
      pic: auth.pic,
      permissions: auth.permissions,
      groups: auth.groups,
      firstname: auth.firstname,
      lastname: auth.lastname,
      token: auth.token,
      refreshToken: auth.refreshToken,
      expiresIn: auth.expiresIn
    } as UserModel;
  }

  private parsePortadorUserFromAuth(auth: any): PortadorUserModel {
    return {
      cpf: auth.cpf,
      documentoAcesso: auth.documentoAcesso,
      idLogin: auth.idLogin,
      isDeviceIdValido: auth.isDeviceIdValido,
      isAntifraudeValidacaoNecessaria: auth.isAntifraudeValidacaoNecessaria,
      encaminharAtendimento: auth.encaminharAtendimento,
      onboardRealizado: auth.onboardRealizado,
      limitesPIXAlterados: auth.limitesPIXAlterados,
      token: auth.token,
      refreshToken: auth.refreshToken,
      expiresIn: auth.expiresIn
    } as PortadorUserModel;
  }

  private getAuthFromLocalStorageIssuer(): AuthModel | undefined {
    try {
      const lsValue = localStorage.getItem(this.authLocalStorageTokenIssuer);
      if (!lsValue) {
        return undefined;
      }
      return JSON.parse(lsValue) as AuthModel;
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }

  public getAuthFromLocalStoragePortador(): PortadorAuthModel | undefined {
    try {
      const lsValue = localStorage.getItem(this.authLocalStorageTokenPortador);
      if (!lsValue) {
        return undefined;
      }
      return JSON.parse(lsValue) as PortadorAuthModel;
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }
}
