import { Injectable } from '@angular/core';
import { Usuario } from '../interfaces/usuario.interface';

const ATUALIZAR_VERSAO = '_valloo_atualizar_app';
const VERSAO_APLICATIVO = '_valloo_versao_app';
const VALLOO_TOKEN = '_valloo_token';
const VALLOO_USER = '_valloo_user';
const VALLOO_BIOMETRIA = '_valloo_biometria';
const VALLOO_PRODUTOS_PERMISSOES = '_valloo_produtos_permissoes';
const VERIFICAR_POSSUI_CONTA_INICIO_APP = '_valloo_possui_conta';
const IMAGEM_AVATAR = 'imagem-avatar';
const ID_LOGIN = '_valloo_id_login';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  constructor() {
  }

  setProdutosPermissoes(permissoes: any[]) {
    localStorage.setItem(VALLOO_PRODUTOS_PERMISSOES, JSON.stringify(permissoes));
  }

  getProdutosPermissoes() {
    return JSON.parse(localStorage.getItem(VALLOO_PRODUTOS_PERMISSOES) as any);
  }

  setAtualizarVersao(atualizar: boolean) {
    localStorage.setItem(ATUALIZAR_VERSAO, JSON.stringify(atualizar));
  }

  atualizarVersao() {
    return JSON.parse(localStorage.getItem(ATUALIZAR_VERSAO) as any);
  }

  setVersaoApp(versaoApp: any) {
    localStorage.setItem(VERSAO_APLICATIVO, JSON.stringify(versaoApp));
  }

  getVersaoApp() {
    return JSON.parse(localStorage.getItem(VERSAO_APLICATIVO) as any);
  }

  setToken(token: string) {
    localStorage.setItem(VALLOO_TOKEN, token);
  }

  getToken() {
    return localStorage.getItem(VALLOO_TOKEN);
  }

  clearToken() {
    localStorage.removeItem(VALLOO_TOKEN);
  }

  setUser(usuario: Usuario) {
    if (usuario.documento) {
      usuario.imagemPerfil = this.getAvatarImage(usuario.documento);
    }
    localStorage.setItem(VALLOO_USER, JSON.stringify(usuario));
  }

  getUser() {
    const user: any = localStorage.getItem(VALLOO_USER);
    return JSON.parse(user);
  }

  setBiometricActive(biometric: any = null) {
    if (!biometric) {
      biometric = {
        first: true,
        active: false
      };
    }
    localStorage.setItem(VALLOO_BIOMETRIA, JSON.stringify(biometric));
  }

  getBiometricActive() {
    const biometria: any = localStorage.getItem(VALLOO_BIOMETRIA);
    return JSON.parse(biometria);
  }

  verificarPossuiContaInicioApp() {
    return JSON.parse(localStorage.getItem(VERIFICAR_POSSUI_CONTA_INICIO_APP) as any);
  }

  marcarPossuiContaInicioApp() {
    localStorage.setItem(VERIFICAR_POSSUI_CONTA_INICIO_APP, JSON.stringify(true));
  }

  setIdLogin(idLogin: any) {
    localStorage.setItem(ID_LOGIN, idLogin);
  }

  getIdLogin() {
    return localStorage.getItem(ID_LOGIN);
  }

  setAvatarImage(img: any, documento: string) {
    localStorage.setItem(IMAGEM_AVATAR + '_' + documento, img);
  }

  getAvatarImage(documento: string) {
    return localStorage.getItem(IMAGEM_AVATAR + '_' + documento);
  }

}
