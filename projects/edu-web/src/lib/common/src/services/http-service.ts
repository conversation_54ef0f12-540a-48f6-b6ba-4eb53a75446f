import { Injectable, Injector } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { EduConfigService } from '../modules/config';

@Injectable({
    providedIn: 'root'
})
export class HttpService {
    protected configService: EduConfigService;
    protected http: HttpClient;

    resourcePath: string = '';

    constructor(
        protected injector: Injector,
        protected eduConfigService: EduConfigService
    ) {
        this.configService = this.eduConfigService;
        this.http = this.injector.get(HttpClient);
    }

    /**
     * Fetches a single resource by its ID.
     * @param id The unique identifier for the resource.
     * @returns Observable<any> An Observable that, on subscription, will send a GET request to the API and emit the fetched data.
     */
    getById(id: number): Observable<any> {
        return this.http.get(
            `${this.configService.baseUrl}/${this.resourcePath}/${id}`
        );
    }

    /**
     * Retrieves all resources, with support for pagination.
     * @param page The page number of the dataset to retrieve.
     * @param size The number of records per page.
     * @returns Observable<any> An Observable that, on subscription, will send a GET request with pagination parameters.
     */
    getAll(page: number = 0, size: number = 10): Observable<any> {
        return this.http.get(
            `${this.configService.baseUrl}/${this.resourcePath}/get-all`,
            {
                params: {
                    page: page.toString(),
                    size: size.toString()
                }
            }
        );
    }

    /**
     * Finds a resource by its ID using a more specific 'find/{id}' endpoint.
     * @param id The unique identifier for the resource.
     * @returns Observable<any> An Observable for the GET request to fetch the resource by its ID.
     */
    findByID(id: number): Observable<any> {
        return this.http.get(
            `${this.configService.baseUrl}/${this.resourcePath}/find/${id}`
        );
    }

    /**
     * Finds all resources that match a specific field and value, with support for pagination.
     * @param field The field to filter by.
     * @param value The value to match in the specified field.
     * @param page The page number for pagination.
     * @param size The number of items per page.
     * @returns Observable<any> An Observable that sends a GET request with filter and pagination parameters.
     */
    findAllBy(
        field: string,
        value: string,
        page: number = 0,
        size: number = 10
    ): Observable<any> {
        return this.http.get(
            `${this.configService.baseUrl}/${this.resourcePath}/all-by`,
            {
                params: new HttpParams()
                    .set('field', field)
                    .set('value', value)
                    .set('page', page.toString())
                    .set('size', size.toString())
            }
        );
    }

    /**
     * Creates a new resource.
     * @param data The data for the new resource to be created.
     * @returns Observable<any> An Observable that, when subscribed to, will send a POST request to create the resource.
     */
    create(data: any): Observable<any> {
        return this.http.post(
            `${this.configService.baseUrl}/${this.resourcePath}`,
            data,
            {
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }

    /**
     * Updates a specific resource by ID.
     * @param id The unique identifier of the resource to update.
     * @param data The data to update the resource with.
     * @returns Observable<any> An Observable that, on subscription, will send a PUT request to update the resource.
     */
    update(id: number, data: any): Observable<any> {
        return this.http.put(
            `${this.configService.baseUrl}/${this.resourcePath}/${id}`,
            data,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }

    /**
     * Deletes a specific resource by ID.
     * @param id The unique identifier of the resource to delete.
     * @returns Observable<any> An Observable that, on subscription, will send a DELETE request to remove the resource.
     */
    delete(id: number): Observable<any> {
        return this.http.delete(
            `${this.configService.baseUrl}/${this.resourcePath}/${id}`
        );
    }
}
