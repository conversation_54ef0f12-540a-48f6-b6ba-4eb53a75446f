import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AccessControlService {

  private permissions: Set<string> = new Set();

  constructor() {
  }

  initialize(permissions: string[]): void {
    this.permissions = new Set(permissions);
  }

  hasPermission(permission: string): boolean {
    return this.permissions.has(permission);
  }

  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.permissions.has(permission));
  }

  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.permissions.has(permission));
  }
}
