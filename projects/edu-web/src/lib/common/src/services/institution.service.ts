import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Institution } from '../interfaces/institution.interface';
import { EduConfigService } from '../modules/config/edu-config.service';

@Injectable({
  providedIn: 'root'
})
export class InstitutionService {
  private currentInstitution$: BehaviorSubject<Institution | null> = new BehaviorSubject<Institution | null>(null);
  private userInstitutions$: BehaviorSubject<Institution[]> = new BehaviorSubject<Institution[]>([]);

  private readonly STORAGE_KEY = 'current-institution';

  constructor(
    private http: HttpClient,
    private configService: EduConfigService
  ) {
    this.loadCurrentInstitutionFromStorage();
  }

  /**
   * Get current institution as observable
   */
  getCurrentInstitution(): Observable<Institution | null> {
    return this.currentInstitution$.asObservable();
  }

  /**
   * Get current institution value
   */
  getCurrentInstitutionValue(): Institution | null {
    return this.currentInstitution$.value;
  }

  /**
   * Get user institutions as observable
   */
  getUserInstitutions(): Observable<Institution[]> {
    return this.userInstitutions$.asObservable();
  }

  /**
   * Get user institutions value
   */
  getUserInstitutionsValue(): Institution[] {
    return this.userInstitutions$.value;
  }

  /**
   * Load user institutions from API
   */
  loadUserInstitutions(): Observable<Institution[]> {
    console.log('🔄 Carregando instituições do usuário...');
    console.log('🌐 URL:', `${this.configService.baseUrl}/api/institutions/user`);

    return this.http.get<Institution[]>(`${this.configService.baseUrl}/api/institutions/user`)
      .pipe(
        tap(institutions => {
          console.log('✅ Instituições recebidas:', institutions);
          console.log('📊 Quantidade de instituições:', institutions.length);

          this.userInstitutions$.next(institutions);

          // If no current institution is set and we have institutions, set the first one
          if (!this.currentInstitution$.value && institutions.length > 0) {
            console.log('🏢 Definindo instituição padrão:', institutions[0]);
            this.setCurrentInstitution(institutions[0]);
          }
        }),
        catchError(error => {
          console.error('❌ Erro ao carregar instituições:', error);
          console.error('🔍 Detalhes do erro:', error.error);
          console.error('📊 Status do erro:', error.status);
          this.userInstitutions$.next([]);
          return throwError(error);
        })
      );
  }

  /**
   * Set current institution
   */
  setCurrentInstitution(institution: Institution): void {
    this.currentInstitution$.next(institution);
    this.saveCurrentInstitutionToStorage(institution);
  }

  /**
   * Clear current institution
   */
  clearCurrentInstitution(): void {
    this.currentInstitution$.next(null);
    this.removeCurrentInstitutionFromStorage();
  }

  /**
   * Get current institution details from API
   */
  getCurrentInstitutionDetails(): Observable<Institution> {
    return this.http.get<Institution>(`${this.configService.baseUrl}/api/institutions/current`);
  }

  /**
   * Check if user has access to multiple institutions
   */
  hasMultipleInstitutions(): boolean {
    return this.userInstitutions$.value.length > 1;
  }

  /**
   * Get current institution ID for headers
   */
  getCurrentInstitutionId(): number | null {
    const current = this.currentInstitution$.value;
    return current ? current.id : null;
  }

  /**
   * Save current institution to localStorage
   */
  private saveCurrentInstitutionToStorage(institution: Institution): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(institution));
    } catch (error) {
      console.warn('Could not save current institution to localStorage:', error);
    }
  }

  /**
   * Load current institution from localStorage
   */
  private loadCurrentInstitutionFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const institution = JSON.parse(stored) as Institution;
        this.currentInstitution$.next(institution);
      }
    } catch (error) {
      console.warn('Could not load current institution from localStorage:', error);
    }
  }

  /**
   * Remove current institution from localStorage
   */
  private removeCurrentInstitutionFromStorage(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Could not remove current institution from localStorage:', error);
    }
  }

  /**
   * Initialize institution service (call after user login)
   */
  initialize(): Observable<Institution[]> {
    return this.loadUserInstitutions();
  }

  /**
   * Clean up on logout
   */
  cleanup(): void {
    this.currentInstitution$.next(null);
    this.userInstitutions$.next([]);
    this.removeCurrentInstitutionFromStorage();
  }
}
