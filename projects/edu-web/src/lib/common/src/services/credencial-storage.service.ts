import { Injectable } from '@angular/core';
import { Credencial } from '../interfaces';

@Injectable({
  providedIn: 'root',
})
export class CredencialStorageService {
  private credencialStorage: string = 'credencial_storage';

  public saveCredencial(credencial: Credencial): void {
    if (credencial) {
      localStorage.setItem(this.credencialStorage, JSON.stringify(credencial));
    }
  }

  public removeCredencial(): void {
    localStorage.removeItem(this.credencialStorage);
  }


  public getCredencial(): Credencial | null {
    try {
      const credencial = localStorage.getItem(this.credencialStorage);
      if (!credencial) {
        return null;
      }
      return JSON.parse(credencial) as Credencial;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}
