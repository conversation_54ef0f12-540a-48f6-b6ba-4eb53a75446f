import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { InstitutionService } from '../services/institution.service';

@Injectable()
export class InstitutionInterceptor implements HttpInterceptor {

  constructor(private institutionService: InstitutionService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // URLs that don't need institution header
    const excludeUrls = [
      '/api/authenticate',
      '/api/auth/google',
      '/api/refresh_authenticate',
      '/api/users/register',
      '/api/users/reset-password',
      '/api/users/confirm-reset-password',
      '/api/users/validate-reset-token',
      '/api/users/verify',
      '/api/institutions/user'
    ];

    console.log('🔍 InstitutionInterceptor - URL:', req.url, 'Method:', req.method);

    // Check if request URL should be excluded
    const shouldExclude = excludeUrls.some(url => req.url.includes(url));
    if (shouldExclude) {
      console.log('❌ URL excluída do interceptor:', req.url);
      return next.handle(req);
    }

    // Get current institution ID
    const institutionId = this.institutionService.getCurrentInstitutionId();
    console.log('🏢 Institution ID atual:', institutionId);

    // If no institution is set, proceed without header
    if (!institutionId) {
      console.log('⚠️ Nenhuma instituição definida, prosseguindo sem header');
      return next.handle(req);
    }

    // Clone request and add institution header
    const clonedRequest = req.clone({
      setHeaders: {
        'X-Institution-Id': institutionId.toString()
      }
    });

    console.log('✅ Header X-Institution-Id adicionado:', institutionId);
    return next.handle(clonedRequest);
  }
}
