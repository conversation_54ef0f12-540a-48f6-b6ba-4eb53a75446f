import { Directive, Input, TemplateRef, ViewContainerRef, OnInit } from '@angular/core';
import { AccessControlService } from '../services/access-control.service';

@Directive({
  selector: '[hasPermission]'
})
export class HasPermissionDirective implements OnInit {
  private permissions: string | string[] = [];
  private requireAll: boolean = true;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private accessControlService: AccessControlService
  ) { }

  @Input() set hasPermission(permissions: string | string[]) {
    this.permissions = permissions;
    this.updateView();
  }

  @Input() set hasPermissionRequireAll(requireAll: boolean) {
    this.requireAll = requireAll;
    this.updateView();
  }

  ngOnInit() {
    this.updateView();
  }

  private updateView() {
    if (!this.permissions) {
      this.viewContainer.clear();
      return;
    }

    const permissionsArray = Array.isArray(this.permissions) ? this.permissions : [this.permissions];
    let hasAccess = false;

    if (this.requireAll) {
      // Lógica AND - usuário deve ter TODAS as permissões
      hasAccess = permissionsArray.every(permission =>
        this.accessControlService.hasPermission(permission)
      );
    } else {
      // Lógica OR - usuário deve ter PELO MENOS UMA das permissões
      hasAccess = permissionsArray.some(permission =>
        this.accessControlService.hasPermission(permission)
      );
    }

    if (hasAccess) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}