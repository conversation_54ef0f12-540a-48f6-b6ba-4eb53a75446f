import { TipoPessoaEnum } from "../enums/tipo-pessoa.enum";

export interface EntidadeFormatada{formatado:string,tipo:TipoPessoaEnum}

class Util {
  static trataEntidade(value: string): { formatado: string, tipo: TipoPessoaEnum } {
    let retorno: string = '';
    let tipo: TipoPessoaEnum;

    if (value.length <= 11) {
      tipo = TipoPessoaEnum.fisica;
      retorno = this.removerPontosVirgulasTracos(value);
    } else {
      tipo = TipoPessoaEnum.juridica;
      retorno = this.removerChaveFromString(value, ['.', '.', '-', '/']);
    }

    return { formatado: retorno, tipo: tipo };
  }

  private static removerPontosVirgulasTracos(value: string): string {
    return value.replace(/[.\-]/g, '');
  }

  private static removerChaveFromString(value: string, charsToRemove: string[]): string {
    let result = value;
    charsToRemove.forEach(char => {
      result = result.split(char).join('');
    });
    return result;
  }

  static padLeftZeros(inputString: string, length: number) {
    if (inputString.length >= length) {
      return inputString;
    }
    let sb = String();
    while (sb.length < length - inputString.length) {
      sb = sb + "0"
    }
    sb = sb + inputString;
    return sb;
  }

  static calcularDigitoVerificadorConta(sequenciaNumerica: number) {
    let sequenciaNumericaAsString = Util.padLeftZeros(sequenciaNumerica.toString(), 8);
    let n = 9;
    let soma = 0;

    for (let i = 0; i < sequenciaNumericaAsString.length; i++) {
      let digito: any = sequenciaNumericaAsString.substring(i, i + 1);
      soma += digito * n;
      n--;
    }
    let restoDaDivisao = soma % 11;
    let digitoVerificador = 11 - restoDaDivisao;

    if (digitoVerificador == 10 || digitoVerificador == 11) {
      return 0;
    } else {
      return digitoVerificador;
    }

  }
}

export { Util };
