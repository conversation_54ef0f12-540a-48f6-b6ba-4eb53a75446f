export class ColumnAction {
  icon?: string;
  iconType?: 'bootstrap' | 'keenicons' | 'fontawesome';
  class?: string;
  buttonStyle?: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'dark' | 'link' = 'secondary';
  type: 'edit' | 'delete' | 'custom';
  callback?: (row: any) => void;

  constructor({
    icon,
    iconType,
    classStr,
    buttonStyle = 'secondary',
    type,
    callback
  }: {
    icon?: string,
    iconType?: 'bootstrap' | 'keenicons' | 'fontawesome',
    classStr?: string,
    buttonStyle?: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'dark' | 'link',
    type: 'edit' | 'delete' | 'custom',
    callback?: (row: any) => void
  }) {
    this.icon = icon;
    this.iconType = iconType;
    this.class = classStr;
    this.buttonStyle = buttonStyle;
    this.type = type;
    this.callback = callback;
  }
}
