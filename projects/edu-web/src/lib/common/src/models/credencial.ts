// export class Credencial {
//   b2b: boolean;
//   contaPagamento: string;
//   credencialMascarada: string;
//   credencialMascaradaReduzida: string;
//   credencialUltimosDigitos: string;
//   dataHoraInclusao: Date;
//   dataSaldo: string;
//   dataValidade: Date;
//   dataValidadeFmt: string;
//   descGrupoStatus: string;
//   descStatus: string;
//   descStatusConta: string;
//   faturaInibida: boolean;
//   grupoStatus: number;
//   idConta: number;
//   idCredencial: number;
//   idPessoa: number;
//   idPlastico: number;
//   idProduto: number;
//   idProdutoInstituicao: number;
//   idProdutoPlataforma: number;
//   mesesValidadeCartaoVitual: number;
//   metodoSegurancaTransacao: number;
//   nomeImpresso: string;
//   nomeProduto: string;
//   permiteInibirFatura: boolean;
//   preparaDataSaldo: string;
//   saldo: number;
//   status: number;
//   statusConta: number;
//   tipoConta: number;
//   urlImagemProduto: string;
//   valorMaxCarga: number;
//   valorMinCarga: number;
//   virtual: boolean;

//   static fromJson(json: any): Credencial {
//     const credencial = new Credencial();
//     credencial.b2b = json.b2b;
//     credencial.contaPagamento = json.contaPagamento;
//     credencial.credencialMascarada = json.credencialMascarada;
//     credencial.credencialMascaradaReduzida = json.credencialMascaradaReduzida;
//     credencial.credencialUltimosDigitos = json.credencialUltimosDigitos;
//     credencial.dataHoraInclusao = new Date(json.dataHoraInclusao);
//     credencial.dataSaldo = json.dataSaldo;
//     credencial.dataValidade = new Date(json.dataValidade);
//     credencial.dataValidadeFmt = json.dataValidadeFmt;
//     credencial.descGrupoStatus = json.descGrupoStatus;
//     credencial.descStatus = json.descStatus;
//     credencial.descStatusConta = json.descStatusConta;
//     credencial.faturaInibida = json.faturaInibida;
//     credencial.grupoStatus = json.grupoStatus;
//     credencial.idConta = json.idConta;
//     credencial.idCredencial = json.idCredencial;
//     credencial.idPessoa = json.idPessoa;
//     credencial.idPlastico = json.idPlastico;
//     credencial.idProduto = json.idProduto;
//     credencial.idProdutoInstituicao = json.idProdutoInstituicao;
//     credencial.idProdutoPlataforma = json.idProdutoPlataforma;
//     credencial.mesesValidadeCartaoVitual = json.mesesValidadeCartaoVitual;
//     credencial.metodoSegurancaTransacao = json.metodoSegurancaTransacao;
//     credencial.nomeImpresso = json.nomeImpresso;
//     credencial.nomeProduto = json.nomeProduto;
//     credencial.permiteInibirFatura = json.permiteInibirFatura;
//     credencial.preparaDataSaldo = json.preparaDataSaldo;
//     credencial.saldo = json.saldo;
//     credencial.status = json.status;
//     credencial.statusConta = json.statusConta;
//     credencial.tipoConta = json.tipoConta;
//     credencial.urlImagemProduto = json.urlImagemProduto;
//     credencial.valorMaxCarga = json.valorMaxCarga;
//     credencial.valorMinCarga = json.valorMinCarga;
//     credencial.virtual = json.virtual;
//     return credencial;
//   }
// }
