import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, Component, Input } from '@angular/core';
import { EduConfigService } from '../../../../../../common/src/modules/config';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  // standalone: true,
  // schemas: [CUSTOM_ELEMENTS_SCHEMA],
  // imports: [CommonModule]
})
export class FooterComponent {
  @Input() appFooterContainerCSSClass: string = '';

  appTitle: string = '';

  currentDateStr: string = new Date().getFullYear().toString();
  constructor(config: EduConfigService) {
    this.appTitle = config.appTitle;
  }
}
