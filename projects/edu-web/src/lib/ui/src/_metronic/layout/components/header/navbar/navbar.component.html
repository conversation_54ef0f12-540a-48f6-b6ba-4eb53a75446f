<!--begin::Institution selector-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <app-institution-selector></app-institution-selector>
</div>
<!--end::Institution selector-->

<!--begin::Theme mode-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <!-- <app-theme-mode-switcher [toggleBtnClass]="btnClass" toggleBtnClass="{`btn-active-light-primary btn-custom w-30px h-30px w-md-40px h-md-40px`}"></app-theme-mode-switcher> -->
</div>
<!--end::Theme mode-->

<!--begin::User menu-->
<div class="app-navbar-item" [ngClass]="itemClass">
  <!--begin::Menu wrapper-->
  <div class="cursor-pointer symbol" [ngClass]="userAvatarClass" data-kt-menu-trigger="{default: 'click'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
    <img src="./assets/media/avatars/blank.png" />
  </div>
  <app-user-inner data-kt-menu='true'></app-user-inner>
  <!--end::Menu wrapper-->
</div>
<!--end::User menu-->

<!--begin::Header menu toggle-->
<ng-container *ngIf="false && appHeaderDefaulMenuDisplay">
  <div class="app-navbar-item d-lg-none ms-2 me-n3" title="Show header menu">
    <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_header_menu_toggle">
      <!-- <app-keenicon name="element-4" class="fs-1" [ngClass]="btnIconClass"></app-keenicon> -->
    </div>
  </div>
</ng-container>
<!--end::Header menu toggle-->
