import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KeeniconComponent } from '../../../shared/keenicon/keenicon.component';
import { NavbarComponent } from './navbar/navbar.component';
import { HeaderComponent } from './header.component';
import { PageTitleComponent } from './page-title/page-title.component';
import { FooterComponent } from '../footer/footer.component';
import { TranslationModule } from '../../../../../../common/src/modules/i18n';
import { UtilityModule } from '../../../../components/vlo/utility/utility.module';
import { ThemeModeSwitcherComponent } from '../../../partials/layout/theme-mode-switcher/theme-mode-switcher.component';
import { UserInnerComponent } from '../../../../components/user-inner/user-inner.component';
import { InstitutionSelectorModule } from '../../../../components/institution-selector/institution-selector.module';

@NgModule({
  declarations: [
    HeaderComponent,
    PageTitleComponent,
    FooterComponent,
    NavbarComponent,
    UserInnerComponent
  ],
  imports: [
    CommonModule,
    TranslationModule,
    UtilityModule,
    KeeniconComponent,
    ThemeModeSwitcherComponent,
    InstitutionSelectorModule
  ],
  exports: [
    HeaderComponent,
    PageTitleComponent,
    FooterComponent,
    NavbarComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class HeaderModule {}
